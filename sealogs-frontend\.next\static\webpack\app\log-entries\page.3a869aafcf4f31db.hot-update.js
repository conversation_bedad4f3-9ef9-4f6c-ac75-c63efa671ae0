"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx":
/*!******************************************************************!*\
  !*** ./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PassengerVehiclePickDrop; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _pvpddgr__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../pvpddgr */ \"(app-pages-browser)/./src/app/ui/logbook/pvpddgr.tsx\");\n/* harmony import */ var _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsRecord */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsRecord.js\");\n/* harmony import */ var _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/tripReport_Stop */ \"(app-pages-browser)/./src/app/offline/models/tripReport_Stop.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsChecklist */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsChecklist.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Create model instances outside component to prevent re-instantiation on every render\nconst dangerousGoodsRecordModel = new _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\nconst tripReport_StopModel = new _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\nconst geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\nconst dangerousGoodsChecklistModel = new _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n\n\n\n\n\n\n\nfunction PassengerVehiclePickDrop(param) {\n    let { geoLocations, currentTrip = false, updateTripReport, selectedEvent = false, tripReport, closeModal, type, logBookConfig, members, locked, tripReport_Stops, setTripReport_Stops, displayDangerousGoods = false, displayDangerousGoodsSailing, setDisplayDangerousGoods, setDisplayDangerousGoodsSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGR, setSelectedDGR, offline = false } = param;\n    _s();\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [arrTime, setArrTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [depTime, setDepTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [cargoOnOff, setCargoOnOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(selectedEvent);\n    const [parentLocation, setParentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [bufferDgr, setBufferDgr] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [dgrChecklist, setDgrChecklist] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [tripEvent, setTripEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [openNewLocationDialog, setOpenNewLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // State for LocationField to track current location selection\n    const [currentEventForLocation, setCurrentEventForLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        geoLocationID: 0,\n        lat: null,\n        long: null\n    });\n    // Local state for input values to prevent focus loss\n    const [localPaxOn, setLocalPaxOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localPaxOff, setLocalPaxOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOn, setLocalVehicleOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOff, setLocalVehicleOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // Memoize displayField results to prevent re-computation on every render\n    const displayFieldResults = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents;\n        const eventTypesConfig = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        const fieldMap = new Map();\n        if ((eventTypesConfig === null || eventTypesConfig === void 0 ? void 0 : eventTypesConfig.length) > 0) {\n            var _eventTypesConfig__customisedComponentFields, _eventTypesConfig_;\n            (_eventTypesConfig_ = eventTypesConfig[0]) === null || _eventTypesConfig_ === void 0 ? void 0 : (_eventTypesConfig__customisedComponentFields = _eventTypesConfig_.customisedComponentFields) === null || _eventTypesConfig__customisedComponentFields === void 0 ? void 0 : _eventTypesConfig__customisedComponentFields.nodes.forEach((field)=>{\n                if (field.status !== \"Off\") {\n                    fieldMap.set(field.fieldName, true);\n                }\n            });\n        }\n        return fieldMap;\n    }, [\n        logBookConfig\n    ]);\n    const displayField = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((fieldName)=>{\n        return displayFieldResults.get(fieldName) || false;\n    }, [\n        displayFieldResults\n    ]);\n    // Stable onChange handlers that only update local state\n    const handlePaxOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOff(e.target.value);\n    }, []);\n    const handlePaxOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOn(e.target.value);\n    }, []);\n    const handleVehicleOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOn(e.target.value);\n    }, []);\n    const handleVehicleOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOff(e.target.value);\n    }, []);\n    // Stable onBlur handlers that update the main state\n    const handlePaxOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handlePaxOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    // Memoize other callback functions\n    const handleLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        console.log(\"\\uD83D\\uDDFA️ LocationChange:\", value);\n        // If value is null or undefined, clear the location\n        if (!value) {\n            console.log(\"\\uD83D\\uDDFA️ Clearing location\");\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: 0,\n                lat: null,\n                long: null\n            });\n            // Update currentEventForLocation for LocationField display\n            setCurrentEventForLocation({\n                geoLocationID: 0,\n                lat: null,\n                long: null\n            });\n            return;\n        }\n        // Handle \"Add new location\" option\n        if (value.value === \"newLocation\") {\n            if (\"geolocation\" in navigator) {\n                navigator.geolocation.getCurrentPosition((param)=>{\n                    let { coords } = param;\n                    const { latitude, longitude } = coords;\n                    setLocation({\n                        latitude,\n                        longitude\n                    });\n                    setOpenNewLocationDialog(true);\n                });\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: \"Geolocation is not supported by your browser\",\n                    variant: \"destructive\"\n                });\n                setOpenNewLocationDialog(true);\n            }\n            return;\n        }\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            console.log(\"\\uD83D\\uDDFA️ Location selected:\", value.label, \"ID:\", value.value);\n            // If the value object has latitude and longitude, update currentLocation\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                console.log(\"\\uD83D\\uDDFA️ Setting coords from location:\", value.latitude, value.longitude);\n                setTripReport_Stops({\n                    ...tripReport_Stops,\n                    geoLocationID: 0,\n                    lat: value.latitude,\n                    long: value.longitude\n                });\n                // Update currentEventForLocation for LocationField display\n                setCurrentEventForLocation({\n                    geoLocationID: 0,\n                    lat: value.latitude,\n                    long: value.longitude\n                });\n                setLocation({\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                });\n            }\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            console.log(\"\\uD83D\\uDDFA️ Coordinates entered:\", value.latitude, value.longitude);\n            // Handle direct coordinates input\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update currentEventForLocation for LocationField display\n            setCurrentEventForLocation({\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update currentLocation\n            setCurrentLocation({\n                latitude: value.latitude,\n                longitude: value.longitude\n            });\n        }\n    }, [\n        tripReport_Stops,\n        setTripReport_Stops,\n        toast,\n        setLocation,\n        setOpenNewLocationDialog,\n        setCurrentLocation,\n        setCurrentEventForLocation\n    ]);\n    const handleParentLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((selectedLocation)=>{\n        setParentLocation((selectedLocation === null || selectedLocation === void 0 ? void 0 : selectedLocation.value) || null);\n    }, [\n        setParentLocation\n    ]);\n    const handleArrTimeChange = (date)=>{\n        const formattedTime = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\");\n        setArrTime(formattedTime);\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            arriveTime: formattedTime,\n            arrTime: formattedTime\n        });\n    };\n    const handleDepTimeChange = (date)=>{\n        setDepTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            depTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"),\n            departTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\")\n        });\n    };\n    // Initialize local state from tripReport_Stops\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (tripReport_Stops) {\n            var _tripReport_Stops_paxOn, _tripReport_Stops_paxOff, _tripReport_Stops_vehicleOn, _tripReport_Stops_vehicleOff;\n            setLocalPaxOn(((_tripReport_Stops_paxOn = tripReport_Stops.paxOn) === null || _tripReport_Stops_paxOn === void 0 ? void 0 : _tripReport_Stops_paxOn.toString()) || \"\");\n            setLocalPaxOff(((_tripReport_Stops_paxOff = tripReport_Stops.paxOff) === null || _tripReport_Stops_paxOff === void 0 ? void 0 : _tripReport_Stops_paxOff.toString()) || \"\");\n            setLocalVehicleOn(((_tripReport_Stops_vehicleOn = tripReport_Stops.vehicleOn) === null || _tripReport_Stops_vehicleOn === void 0 ? void 0 : _tripReport_Stops_vehicleOn.toString()) || \"\");\n            setLocalVehicleOff(((_tripReport_Stops_vehicleOff = tripReport_Stops.vehicleOff) === null || _tripReport_Stops_vehicleOff === void 0 ? void 0 : _tripReport_Stops_vehicleOff.toString()) || \"\");\n        }\n    }, [\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff\n    ]);\n    // Only initialize from existing records when editing (no automatic sync)\n    const [hasInitialized, setHasInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!hasInitialized && selectedEvent && tripReport_Stops && (tripReport_Stops.geoLocationID || tripReport_Stops.lat || tripReport_Stops.long)) {\n            console.log(\"\\uD83D\\uDDFA️ One-time init from existing record:\", {\n                geoLocationID: tripReport_Stops.geoLocationID,\n                lat: tripReport_Stops.lat,\n                long: tripReport_Stops.long\n            });\n            setCurrentEventForLocation({\n                geoLocationID: tripReport_Stops.geoLocationID || 0,\n                lat: tripReport_Stops.lat || null,\n                long: tripReport_Stops.long || null\n            });\n            setHasInitialized(true);\n        }\n    }, [\n        selectedEvent,\n        hasInitialized\n    ]) // Removed tripReport_Stops dependencies to prevent re-runs\n    ;\n    // Track currentEventForLocation changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDDFA️ currentEventForLocation updated:\", currentEventForLocation);\n    }, [\n        currentEventForLocation\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentTripReport_Stop(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        }\n    }, [\n        selectedEvent\n    ]);\n    const offlineCreateDangerousGoodsChecklist = async ()=>{\n        // createDangerousGoodsChecklist\n        const delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\n        await delay(2000);\n        const data = await dangerousGoodsChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n        });\n        setDgrChecklist(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (currentEvent) {\n            getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            setDgrChecklist(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.dangerousGoodsChecklist);\n        } else {\n            // Initialize default values for new records\n            if (!tripReport_Stops) {\n                setTripReport_Stops({\n                    paxOn: 0,\n                    paxOff: 0,\n                    vehicleOn: 0,\n                    vehicleOff: 0\n                });\n            }\n        }\n    }, [\n        currentEvent\n    ]);\n    const getCurrentTripReport_Stop = async (id)=>{\n        if (offline) {\n            // tripReport_Stop\n            const event = await tripReport_StopModel.getById(id);\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3, _event_stopLocation4, _event_stopLocation5;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long\n                    });\n                    // Initialize currentEventForLocation for LocationField\n                    const locationData = {\n                        geoLocationID: event.stopLocationID || 0,\n                        lat: ((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) || (event === null || event === void 0 ? void 0 : event.lat) || null,\n                        long: ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long) || (event === null || event === void 0 ? void 0 : event.long) || null\n                    };\n                    console.log(\"\\uD83D\\uDDFA️ Loading offline data:\", locationData);\n                    setCurrentEventForLocation(locationData);\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat) && ((_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long)) {\n                        var _event_stopLocation6, _event_stopLocation7;\n                        console.log(\"\\uD83D\\uDDFA️ Stop location:\", event);\n                        setCurrentLocation({\n                            latitude: (_event_stopLocation6 = event.stopLocation) === null || _event_stopLocation6 === void 0 ? void 0 : _event_stopLocation6.lat,\n                            longitude: (_event_stopLocation7 = event.stopLocation) === null || _event_stopLocation7 === void 0 ? void 0 : _event_stopLocation7.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        console.log(\"\\uD83D\\uDDFA️ Stop lat long:\", event);\n                        setCurrentLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        } else {\n            tripReport_Stop({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const getBufferDgr = async (id)=>{\n        if (bufferDgr.length > 0) {\n            const dgr = bufferDgr.map((d)=>{\n                return +d.id;\n            });\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    ...dgr,\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            ...dgr,\n                            +id\n                        ]\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            +id\n                        ]\n                    }\n                });\n            }\n        }\n    };\n    const [getDgrList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetDangerousGoodsRecords, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setBufferDgr(data.readDangerousGoodsRecords.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error getting buffer dgr\", error);\n        }\n    });\n    const [tripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetTripReport_Stop, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripReport_Stop;\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setDisplayDangerousGoodsSailing(displayDangerousGoodsSailing !== null ? displayDangerousGoodsSailing : event === null || event === void 0 ? void 0 : event.designatedDangerousGoodsSailing);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3, _event_stopLocation4, _event_stopLocation5;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long,\n                        designatedDangerousGoodsSailing: event.designatedDangerousGoodsSailing\n                    });\n                    // Initialize currentEventForLocation for LocationField\n                    const locationData = {\n                        geoLocationID: event.stopLocationID || 0,\n                        lat: ((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) || (event === null || event === void 0 ? void 0 : event.lat) || null,\n                        long: ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long) || (event === null || event === void 0 ? void 0 : event.long) || null\n                    };\n                    console.log(\"\\uD83D\\uDDFA️ Loading online data:\", locationData);\n                    setCurrentEventForLocation(locationData);\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat) && ((_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long)) {\n                        var _event_stopLocation6, _event_stopLocation7;\n                        console.log(\"\\uD83D\\uDDFA️ stopLocation trip:\", event);\n                        setCurrentLocation({\n                            latitude: (_event_stopLocation6 = event.stopLocation) === null || _event_stopLocation6 === void 0 ? void 0 : _event_stopLocation6.lat,\n                            longitude: (_event_stopLocation7 = event.stopLocation) === null || _event_stopLocation7 === void 0 ? void 0 : _event_stopLocation7.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        console.log(\"\\uD83D\\uDDFA️ Stop lat long trip:\", event);\n                        setCurrentLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    // Memoize locations array to prevent unnecessary re-creation\n    const memoizedLocations = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (!geoLocations) return [];\n        return [\n            {\n                label: \"--- Add new location ---\",\n                value: \"newLocation\"\n            },\n            ...geoLocations.filter((location)=>location.title).map((location)=>({\n                    label: location.title,\n                    value: location.id,\n                    latitude: location.lat,\n                    longitude: location.long\n                }))\n        ];\n    }, [\n        geoLocations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setLocations(memoizedLocations);\n    }, [\n        memoizedLocations\n    ]);\n    const validateForm = ()=>{\n        // Validate stopLocationID\n        const stopLocationID = +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID);\n        if (!stopLocationID || stopLocationID <= 0) {\n            toast({\n                title: \"Error\",\n                description: \"Please select a trip stop location\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        // Validate arriveTime - check both arrTime and tripReport_Stops.arriveTime/arrTime\n        const arriveTimeValue = arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime);\n        // Use isEmpty but also check for false value since arrTime's initial state is false\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(arriveTimeValue) || arriveTimeValue === false) {\n            toast({\n                title: \"Error\",\n                description: \"Please enter an arrival time\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        return true;\n    };\n    console.log(\"currentLocation\", currentLocation);\n    const handleSave = async ()=>{\n        // Validate form before saving\n        if (!validateForm()) {\n            return;\n        }\n        // Use local state values for the most current input data\n        const paxOnValue = localPaxOn !== \"\" ? +localPaxOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn) || 0;\n        const paxOffValue = localPaxOff !== \"\" ? +localPaxOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff) || 0;\n        const vehicleOnValue = localVehicleOn !== \"\" ? +localVehicleOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn) || 0;\n        const vehicleOffValue = localVehicleOff !== \"\" ? +localVehicleOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff) || 0;\n        const variables = {\n            input: {\n                arriveTime: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime),\n                departTime: depTime ? depTime : tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.departTime,\n                paxJoined: paxOnValue,\n                paxDeparted: paxOffValue,\n                vehiclesJoined: isNaN(vehicleOnValue) ? 0 : vehicleOnValue,\n                vehiclesDeparted: isNaN(vehicleOffValue) ? 0 : vehicleOffValue,\n                stopLocationID: +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID),\n                otherCargo: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo,\n                comments: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments,\n                lat: currentLocation.latitude.toString(),\n                long: currentLocation.longitude.toString(),\n                dangerousGoodsChecklistID: +(dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.id),\n                designatedDangerousGoodsSailing: displayDangerousGoodsSailing\n            }\n        };\n        if (currentEvent) {\n            if (offline) {\n                // updateTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                    ...variables.input\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n            } else {\n                updateTripReport_Stop({\n                    variables: {\n                        input: {\n                            id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            // Set default values for new records\n            variables.input.paxJoined = variables.input.paxJoined || 0;\n            variables.input.paxDeparted = variables.input.paxDeparted || 0;\n            if (offline) {\n                // createTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    ...variables.input,\n                    logBookEntrySectionID: currentTrip.id,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                if (bufferDgr.length > 0) {\n                    Promise.all(bufferDgr.map(async (dgr)=>{\n                        // updateDangerousGoodsRecord\n                        const dgrData = await dangerousGoodsRecordModel.save({\n                            id: dgr.id,\n                            tripReport_StopID: data.id,\n                            type: dgr.type,\n                            comment: dgr.comment\n                        });\n                        // Clear any existing toasts\n                        if ((currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0) {\n                            await getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                        } else {\n                            if (dgrData) {\n                                await getBufferDgr(dgrData.id);\n                            }\n                        }\n                        // createDangerousGoodsChecklist\n                        const dgChecklistData = await dangerousGoodsChecklistModel.save({\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                            tripReport_StopID: data.id,\n                            vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                            bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                            twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                            fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                            noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                            spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                            fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                            dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                            loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                            msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                            anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                            safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                            vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                        });\n                        setDgrChecklist(dgChecklistData);\n                    }));\n                }\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                closeModal();\n            } else {\n                createTripReport_Stop({\n                    variables: {\n                        input: {\n                            ...variables.input,\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createDangerousGoodsChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateDangerousGoodsChecklist, {\n        onCompleted: (response)=>{\n            const data = response.createDangerousGoodsChecklist;\n            setDgrChecklist(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating dangerous goods\", error);\n        }\n    });\n    const [updateDangerousGoodsRecord] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateDangerousGoodsRecord, {\n        onCompleted: (response)=>{\n            const data = response.updateDangerousGoodsRecord;\n            // Process the response\n            (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0 ? getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) : getBufferDgr(data.id);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating dangerous goods record\", error);\n            toast({\n                title: \"Error\",\n                description: \"Error updating dangerous goods record\",\n                variant: \"destructive\"\n            });\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            if (bufferDgr.length > 0) {\n                bufferDgr.map((dgr)=>{\n                    updateDangerousGoodsRecord({\n                        variables: {\n                            input: {\n                                id: dgr.id,\n                                tripReport_StopID: data.id,\n                                type: dgr.type,\n                                comment: dgr.comment\n                            }\n                        }\n                    });\n                    createDangerousGoodsChecklist({\n                        variables: {\n                            input: {\n                                tripReport_StopID: data.id,\n                                vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                                bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                                twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                                fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                                noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                                spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                                fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                                dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                                loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                                msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                                anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                                safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                                vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                            }\n                        }\n                    });\n                });\n            }\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const [updateTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating passenger drop facility\", error);\n        }\n    });\n    const handleCreateNewLocation = async ()=>{\n        const title = document.getElementById(\"new-location-title\");\n        const latitude = document.getElementById(\"new-location-latitude\");\n        const longitude = document.getElementById(\"new-location-longitude\");\n        if (title && latitude && longitude) {\n            if (offline) {\n                // createGeoLocation\n                const data = await geoLocationModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                    title: title.value,\n                    lat: +latitude.value,\n                    long: +longitude.value,\n                    parentID: parentLocation\n                });\n                setLocations([\n                    ...locations,\n                    {\n                        label: data.title,\n                        value: data.id,\n                        latitude: data.lat,\n                        longitude: data.long\n                    }\n                ]);\n                setTripReport_Stops({\n                    ...tripReport_Stops,\n                    geoLocationID: data.id\n                });\n                setOpenNewLocationDialog(false);\n            } else {\n                createGeoLocation({\n                    variables: {\n                        input: {\n                            title: title.value,\n                            lat: +latitude.value,\n                            long: +longitude.value,\n                            parentID: parentLocation\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createGeoLocation] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CREATE_GEO_LOCATION, {\n        onCompleted: (response)=>{\n            const data = response.createGeoLocation;\n            setLocations([\n                ...locations,\n                {\n                    label: data.title,\n                    value: data.id,\n                    latitude: data.lat,\n                    longitude: data.long\n                }\n            ]);\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: data.id\n            });\n            setOpenNewLocationDialog(false);\n        },\n        onError: (error)=>{\n            toast({\n                title: \"Error\",\n                description: \"Error creating GeoLocation: \" + error.message,\n                variant: \"destructive\"\n            });\n            setOpenNewLocationDialog(false);\n            console.error(\"Error creating new location\", error);\n        }\n    });\n    var _tripReport_Stops_depTime;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.P, {\n                className: \"max-w-[40rem] leading-loose\",\n                children: \"For recording trip stops where passengers, cargo and/or vehicles maybe getting on and off.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1021,\n                columnNumber: 13\n            }, this),\n            displayField(type + \"Location\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Location of trip stop\",\n                htmlFor: \"trip-location\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    offline: offline,\n                    setCurrentLocation: setCurrentLocation,\n                    handleLocationChange: handleLocationChangeCallback,\n                    currentEvent: currentEventForLocation\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1030,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1026,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Arrival\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Arrival Time\",\n                htmlFor: \"arrival-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime) || \"\",\n                    buttonLabel: \"Arrive now\",\n                    handleTimeChange: handleArrTimeChange,\n                    timeID: \"arrival-time\",\n                    fieldName: \"Arrival Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1043,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1039,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Departure\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Departure Time\",\n                htmlFor: \"departure-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: depTime ? depTime : (_tripReport_Stops_depTime = tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.depTime) !== null && _tripReport_Stops_depTime !== void 0 ? _tripReport_Stops_depTime : \"\",\n                    buttonLabel: \"Depart now\",\n                    handleTimeChange: handleDepTimeChange,\n                    timeID: \"departure-time\",\n                    fieldName: \"Departure Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1062,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1058,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"PaxPickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers off\",\n                        htmlFor: \"paxOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOff\",\n                            name: \"paxOff\",\n                            type: \"number\",\n                            value: localPaxOff,\n                            placeholder: \"Pax off\",\n                            min: \"0\",\n                            onChange: handlePaxOffChange,\n                            onBlur: handlePaxOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1080,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1076,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers on\",\n                        htmlFor: \"paxOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOn\",\n                            name: \"paxOn\",\n                            type: \"number\",\n                            value: localPaxOn,\n                            placeholder: \"Pax on\",\n                            min: \"0\",\n                            onChange: handlePaxOnChange,\n                            onBlur: handlePaxOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1095,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1091,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1074,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"VehiclePickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles on\",\n                        htmlFor: \"vehicleOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOn\",\n                            name: \"vehicleOn\",\n                            type: \"number\",\n                            value: localVehicleOn,\n                            placeholder: \"Vehicles getting on\",\n                            min: \"0\",\n                            onChange: handleVehicleOnChange,\n                            onBlur: handleVehicleOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1115,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1111,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles off\",\n                        htmlFor: \"vehicleOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOff\",\n                            name: \"vehicleOff\",\n                            type: \"number\",\n                            value: localVehicleOff,\n                            placeholder: \"Vehicles getting off\",\n                            min: \"0\",\n                            onChange: handleVehicleOffChange,\n                            onBlur: handleVehicleOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1130,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1126,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1109,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"OtherCargo\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Cargo (if any)\",\n                htmlFor: \"cargo-onOff\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"cargo-onOff\",\n                    placeholder: \"Other cargo on and off\",\n                    value: cargoOnOff !== \"\" ? cargoOnOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo) || \"\",\n                    onChange: (e)=>{\n                        setCargoOnOff(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            otherCargo: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1148,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1144,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pvpddgr__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                offline: offline,\n                locked: locked,\n                currentTrip: currentTrip,\n                logBookConfig: logBookConfig,\n                selectedDGR: selectedDGR,\n                setSelectedDGR: setSelectedDGR,\n                members: members,\n                displayDangerousGoods: displayDangerousGoods,\n                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                allDangerousGoods: allPVPDDangerousGoods,\n                setAllDangerousGoods: setAllPVPDDangerousGoods,\n                currentEvent: tripEvent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1169,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Comments\",\n                htmlFor: \"comments\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"comments\",\n                    placeholder: \"Comments\",\n                    value: comments !== \"\" ? comments : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments) || \"\",\n                    onChange: (e)=>{\n                        setComments(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            comments: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1192,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1188,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                        onClick: ()=>closeModal(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1212,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"primary\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                        onClick: locked ? ()=>{} : handleSave,\n                        disabled: locked,\n                        children: selectedEvent ? \"Update\" : \"Save\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1218,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1211,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.AlertDialogNew, {\n                openDialog: openNewLocationDialog,\n                setOpenDialog: setOpenNewLocationDialog,\n                actionText: \"Add New Location\",\n                handleCreate: handleCreateNewLocation,\n                title: \"Add New Location\",\n                description: \"Create a new location for trip stops\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-title\",\n                            type: \"text\",\n                            \"aria-describedby\": \"title-error\",\n                            required: true,\n                            placeholder: \"Location Title\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1234,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1233,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__.Combobox, {\n                            options: locations || [],\n                            onChange: handleParentLocationChangeCallback,\n                            placeholder: \"Parent Location (Optional)\",\n                            buttonClassName: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1243,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1242,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-latitude\",\n                            type: \"text\",\n                            defaultValue: location.latitude,\n                            \"aria-describedby\": \"latitude-error\",\n                            required: true,\n                            placeholder: \"Latitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1251,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1250,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-longitude\",\n                            type: \"text\",\n                            defaultValue: location.longitude,\n                            \"aria-describedby\": \"longitude-error\",\n                            required: true,\n                            placeholder: \"Longitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1261,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1260,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1226,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n        lineNumber: 1020,\n        columnNumber: 9\n    }, this);\n}\n_s(PassengerVehiclePickDrop, \"1SZ884mojrQwYbjj1rzEYaLln5k=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation\n    ];\n});\n_c = PassengerVehiclePickDrop;\nvar _c;\n$RefreshReg$(_c, \"PassengerVehiclePickDrop\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx\n"));

/***/ })

});