"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx":
/*!******************************************************************!*\
  !*** ./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PassengerVehiclePickDrop; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _pvpddgr__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../pvpddgr */ \"(app-pages-browser)/./src/app/ui/logbook/pvpddgr.tsx\");\n/* harmony import */ var _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsRecord */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsRecord.js\");\n/* harmony import */ var _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/tripReport_Stop */ \"(app-pages-browser)/./src/app/offline/models/tripReport_Stop.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsChecklist */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsChecklist.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Create model instances outside component to prevent re-instantiation on every render\nconst dangerousGoodsRecordModel = new _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\nconst tripReport_StopModel = new _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\nconst geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\nconst dangerousGoodsChecklistModel = new _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n\n\n\n\n\n\n\nfunction PassengerVehiclePickDrop(param) {\n    let { geoLocations, currentTrip = false, updateTripReport, selectedEvent = false, tripReport, closeModal, type, logBookConfig, members, locked, tripReport_Stops, setTripReport_Stops, displayDangerousGoods = false, displayDangerousGoodsSailing, setDisplayDangerousGoods, setDisplayDangerousGoodsSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGR, setSelectedDGR, offline = false } = param;\n    _s();\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [arrTime, setArrTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [depTime, setDepTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [cargoOnOff, setCargoOnOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(selectedEvent);\n    const [parentLocation, setParentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [bufferDgr, setBufferDgr] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [dgrChecklist, setDgrChecklist] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [tripEvent, setTripEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [openNewLocationDialog, setOpenNewLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // State for LocationField to track current location selection\n    const [currentEventForLocation, setCurrentEventForLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        geoLocationID: 0,\n        lat: null,\n        long: null\n    });\n    // Local state for input values to prevent focus loss\n    const [localPaxOn, setLocalPaxOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localPaxOff, setLocalPaxOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOn, setLocalVehicleOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOff, setLocalVehicleOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // Memoize displayField results to prevent re-computation on every render\n    const displayFieldResults = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents;\n        const eventTypesConfig = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        const fieldMap = new Map();\n        if ((eventTypesConfig === null || eventTypesConfig === void 0 ? void 0 : eventTypesConfig.length) > 0) {\n            var _eventTypesConfig__customisedComponentFields, _eventTypesConfig_;\n            (_eventTypesConfig_ = eventTypesConfig[0]) === null || _eventTypesConfig_ === void 0 ? void 0 : (_eventTypesConfig__customisedComponentFields = _eventTypesConfig_.customisedComponentFields) === null || _eventTypesConfig__customisedComponentFields === void 0 ? void 0 : _eventTypesConfig__customisedComponentFields.nodes.forEach((field)=>{\n                if (field.status !== \"Off\") {\n                    fieldMap.set(field.fieldName, true);\n                }\n            });\n        }\n        return fieldMap;\n    }, [\n        logBookConfig\n    ]);\n    const displayField = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((fieldName)=>{\n        return displayFieldResults.get(fieldName) || false;\n    }, [\n        displayFieldResults\n    ]);\n    // Stable onChange handlers that only update local state\n    const handlePaxOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOff(e.target.value);\n    }, []);\n    const handlePaxOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOn(e.target.value);\n    }, []);\n    const handleVehicleOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOn(e.target.value);\n    }, []);\n    const handleVehicleOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOff(e.target.value);\n    }, []);\n    // Stable onBlur handlers that update the main state\n    const handlePaxOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handlePaxOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    // Memoize other callback functions\n    const handleLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        console.log(\"\\uD83D\\uDDFA️ LocationChange:\", value);\n        // If value is null or undefined, clear the location\n        if (!value) {\n            console.log(\"\\uD83D\\uDDFA️ Clearing location\");\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: 0,\n                lat: null,\n                long: null\n            });\n            // Update currentEventForLocation for LocationField display\n            setCurrentEventForLocation({\n                geoLocationID: 0,\n                lat: null,\n                long: null\n            });\n            return;\n        }\n        // Handle \"Add new location\" option\n        if (value.value === \"newLocation\") {\n            if (\"geolocation\" in navigator) {\n                navigator.geolocation.getCurrentPosition((param)=>{\n                    let { coords } = param;\n                    const { latitude, longitude } = coords;\n                    setLocation({\n                        latitude,\n                        longitude\n                    });\n                    setOpenNewLocationDialog(true);\n                });\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: \"Geolocation is not supported by your browser\",\n                    variant: \"destructive\"\n                });\n                setOpenNewLocationDialog(true);\n            }\n            return;\n        }\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            console.log(\"\\uD83D\\uDDFA️ Location selected:\", value.label, \"ID:\", value.value);\n            // If the value object has latitude and longitude, update currentLocation\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                console.log(\"\\uD83D\\uDDFA️ Setting coords from location:\", value.latitude, value.longitude);\n                setTripReport_Stops({\n                    ...tripReport_Stops,\n                    geoLocationID: 0,\n                    lat: value.latitude,\n                    long: value.longitude\n                });\n                // Update currentEventForLocation for LocationField display\n                setCurrentEventForLocation({\n                    geoLocationID: 0,\n                    lat: value.latitude,\n                    long: value.longitude\n                });\n                setLocation({\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                });\n            }\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            console.log(\"\\uD83D\\uDDFA️ Coordinates entered:\", value.latitude, value.longitude);\n            // Handle direct coordinates input\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update currentEventForLocation for LocationField display\n            setCurrentEventForLocation({\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update location coordinates\n            setLocation({\n                latitude: value.latitude,\n                longitude: value.longitude\n            });\n        }\n    }, [\n        tripReport_Stops,\n        setTripReport_Stops,\n        toast,\n        setLocation,\n        setOpenNewLocationDialog,\n        setCurrentEventForLocation\n    ]);\n    const handleParentLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((selectedLocation)=>{\n        setParentLocation((selectedLocation === null || selectedLocation === void 0 ? void 0 : selectedLocation.value) || null);\n    }, [\n        setParentLocation\n    ]);\n    const handleArrTimeChange = (date)=>{\n        const formattedTime = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\");\n        setArrTime(formattedTime);\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            arriveTime: formattedTime,\n            arrTime: formattedTime\n        });\n    };\n    const handleDepTimeChange = (date)=>{\n        setDepTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            depTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"),\n            departTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\")\n        });\n    };\n    // Initialize local state from tripReport_Stops\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (tripReport_Stops) {\n            var _tripReport_Stops_paxOn, _tripReport_Stops_paxOff, _tripReport_Stops_vehicleOn, _tripReport_Stops_vehicleOff;\n            setLocalPaxOn(((_tripReport_Stops_paxOn = tripReport_Stops.paxOn) === null || _tripReport_Stops_paxOn === void 0 ? void 0 : _tripReport_Stops_paxOn.toString()) || \"\");\n            setLocalPaxOff(((_tripReport_Stops_paxOff = tripReport_Stops.paxOff) === null || _tripReport_Stops_paxOff === void 0 ? void 0 : _tripReport_Stops_paxOff.toString()) || \"\");\n            setLocalVehicleOn(((_tripReport_Stops_vehicleOn = tripReport_Stops.vehicleOn) === null || _tripReport_Stops_vehicleOn === void 0 ? void 0 : _tripReport_Stops_vehicleOn.toString()) || \"\");\n            setLocalVehicleOff(((_tripReport_Stops_vehicleOff = tripReport_Stops.vehicleOff) === null || _tripReport_Stops_vehicleOff === void 0 ? void 0 : _tripReport_Stops_vehicleOff.toString()) || \"\");\n        }\n    }, [\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff\n    ]);\n    // Only initialize from existing records when editing (no automatic sync)\n    const [hasInitialized, setHasInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!hasInitialized && selectedEvent && tripReport_Stops && (tripReport_Stops.geoLocationID || tripReport_Stops.lat || tripReport_Stops.long)) {\n            console.log(\"\\uD83D\\uDDFA️ One-time init from existing record:\", {\n                geoLocationID: tripReport_Stops.geoLocationID,\n                lat: tripReport_Stops.lat,\n                long: tripReport_Stops.long\n            });\n            setCurrentEventForLocation({\n                geoLocationID: tripReport_Stops.geoLocationID || 0,\n                lat: tripReport_Stops.lat || null,\n                long: tripReport_Stops.long || null\n            });\n            setHasInitialized(true);\n        }\n    }, [\n        selectedEvent,\n        hasInitialized\n    ]) // Removed tripReport_Stops dependencies to prevent re-runs\n    ;\n    // Track currentEventForLocation changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDDFA️ currentEventForLocation updated:\", currentEventForLocation);\n    }, [\n        currentEventForLocation\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentTripReport_Stop(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        }\n    }, [\n        selectedEvent\n    ]);\n    const offlineCreateDangerousGoodsChecklist = async ()=>{\n        // createDangerousGoodsChecklist\n        const delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\n        await delay(2000);\n        const data = await dangerousGoodsChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n        });\n        setDgrChecklist(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (currentEvent) {\n            getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            setDgrChecklist(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.dangerousGoodsChecklist);\n        } else {\n            // Initialize default values for new records\n            if (!tripReport_Stops) {\n                setTripReport_Stops({\n                    paxOn: 0,\n                    paxOff: 0,\n                    vehicleOn: 0,\n                    vehicleOff: 0\n                });\n            }\n        }\n    }, [\n        currentEvent\n    ]);\n    const getCurrentTripReport_Stop = async (id)=>{\n        if (offline) {\n            // tripReport_Stop\n            const event = await tripReport_StopModel.getById(id);\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3, _event_stopLocation4, _event_stopLocation5;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long\n                    });\n                    // Initialize currentEventForLocation for LocationField\n                    const locationData = {\n                        geoLocationID: event.stopLocationID || 0,\n                        lat: ((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) || (event === null || event === void 0 ? void 0 : event.lat) || null,\n                        long: ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long) || (event === null || event === void 0 ? void 0 : event.long) || null\n                    };\n                    console.log(\"\\uD83D\\uDDFA️ Loading offline data:\", locationData);\n                    setCurrentEventForLocation(locationData);\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat) && ((_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long)) {\n                        var _event_stopLocation6, _event_stopLocation7;\n                        console.log(\"\\uD83D\\uDDFA️ Stop location:\", event);\n                        setLocation({\n                            latitude: (_event_stopLocation6 = event.stopLocation) === null || _event_stopLocation6 === void 0 ? void 0 : _event_stopLocation6.lat,\n                            longitude: (_event_stopLocation7 = event.stopLocation) === null || _event_stopLocation7 === void 0 ? void 0 : _event_stopLocation7.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        console.log(\"\\uD83D\\uDDFA️ Stop lat long:\", event);\n                        setLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        } else {\n            tripReport_Stop({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const getBufferDgr = async (id)=>{\n        if (bufferDgr.length > 0) {\n            const dgr = bufferDgr.map((d)=>{\n                return +d.id;\n            });\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    ...dgr,\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            ...dgr,\n                            +id\n                        ]\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            +id\n                        ]\n                    }\n                });\n            }\n        }\n    };\n    const [getDgrList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetDangerousGoodsRecords, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setBufferDgr(data.readDangerousGoodsRecords.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error getting buffer dgr\", error);\n        }\n    });\n    const [tripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetTripReport_Stop, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripReport_Stop;\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setDisplayDangerousGoodsSailing(displayDangerousGoodsSailing !== null ? displayDangerousGoodsSailing : event === null || event === void 0 ? void 0 : event.designatedDangerousGoodsSailing);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3, _event_stopLocation4, _event_stopLocation5;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long,\n                        designatedDangerousGoodsSailing: event.designatedDangerousGoodsSailing\n                    });\n                    // Initialize currentEventForLocation for LocationField\n                    const locationData = {\n                        geoLocationID: event.stopLocationID || 0,\n                        lat: ((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) || (event === null || event === void 0 ? void 0 : event.lat) || null,\n                        long: ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long) || (event === null || event === void 0 ? void 0 : event.long) || null\n                    };\n                    console.log(\"\\uD83D\\uDDFA️ Loading online data:\", locationData);\n                    setCurrentEventForLocation(locationData);\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat) && ((_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long)) {\n                        var _event_stopLocation6, _event_stopLocation7;\n                        console.log(\"\\uD83D\\uDDFA️ stopLocation trip:\", event);\n                        setLocation({\n                            latitude: (_event_stopLocation6 = event.stopLocation) === null || _event_stopLocation6 === void 0 ? void 0 : _event_stopLocation6.lat,\n                            longitude: (_event_stopLocation7 = event.stopLocation) === null || _event_stopLocation7 === void 0 ? void 0 : _event_stopLocation7.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        console.log(\"\\uD83D\\uDDFA️ Stop lat long trip:\", event);\n                        setLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    // Memoize locations array to prevent unnecessary re-creation\n    const memoizedLocations = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (!geoLocations) return [];\n        return [\n            {\n                label: \"--- Add new location ---\",\n                value: \"newLocation\"\n            },\n            ...geoLocations.filter((location)=>location.title).map((location)=>({\n                    label: location.title,\n                    value: location.id,\n                    latitude: location.lat,\n                    longitude: location.long\n                }))\n        ];\n    }, [\n        geoLocations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setLocations(memoizedLocations);\n    }, [\n        memoizedLocations\n    ]);\n    const validateForm = ()=>{\n        // Validate stopLocationID\n        const stopLocationID = +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID);\n        if (!stopLocationID || stopLocationID <= 0) {\n            toast({\n                title: \"Error\",\n                description: \"Please select a trip stop location\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        // Validate arriveTime - check both arrTime and tripReport_Stops.arriveTime/arrTime\n        const arriveTimeValue = arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime);\n        // Use isEmpty but also check for false value since arrTime's initial state is false\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(arriveTimeValue) || arriveTimeValue === false) {\n            toast({\n                title: \"Error\",\n                description: \"Please enter an arrival time\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        return true;\n    };\n    console.log(\"location\", location);\n    const handleSave = async ()=>{\n        // Validate form before saving\n        if (!validateForm()) {\n            return;\n        }\n        // Use local state values for the most current input data\n        const paxOnValue = localPaxOn !== \"\" ? +localPaxOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn) || 0;\n        const paxOffValue = localPaxOff !== \"\" ? +localPaxOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff) || 0;\n        const vehicleOnValue = localVehicleOn !== \"\" ? +localVehicleOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn) || 0;\n        const vehicleOffValue = localVehicleOff !== \"\" ? +localVehicleOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff) || 0;\n        const variables = {\n            input: {\n                arriveTime: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime),\n                departTime: depTime ? depTime : tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.departTime,\n                paxJoined: paxOnValue,\n                paxDeparted: paxOffValue,\n                vehiclesJoined: isNaN(vehicleOnValue) ? 0 : vehicleOnValue,\n                vehiclesDeparted: isNaN(vehicleOffValue) ? 0 : vehicleOffValue,\n                stopLocationID: +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID),\n                otherCargo: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo,\n                comments: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments,\n                lat: currentLocation.latitude.toString(),\n                long: currentLocation.longitude.toString(),\n                dangerousGoodsChecklistID: +(dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.id),\n                designatedDangerousGoodsSailing: displayDangerousGoodsSailing\n            }\n        };\n        if (currentEvent) {\n            if (offline) {\n                // updateTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                    ...variables.input\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n            } else {\n                updateTripReport_Stop({\n                    variables: {\n                        input: {\n                            id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            // Set default values for new records\n            variables.input.paxJoined = variables.input.paxJoined || 0;\n            variables.input.paxDeparted = variables.input.paxDeparted || 0;\n            if (offline) {\n                // createTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    ...variables.input,\n                    logBookEntrySectionID: currentTrip.id,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                if (bufferDgr.length > 0) {\n                    Promise.all(bufferDgr.map(async (dgr)=>{\n                        // updateDangerousGoodsRecord\n                        const dgrData = await dangerousGoodsRecordModel.save({\n                            id: dgr.id,\n                            tripReport_StopID: data.id,\n                            type: dgr.type,\n                            comment: dgr.comment\n                        });\n                        // Clear any existing toasts\n                        if ((currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0) {\n                            await getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                        } else {\n                            if (dgrData) {\n                                await getBufferDgr(dgrData.id);\n                            }\n                        }\n                        // createDangerousGoodsChecklist\n                        const dgChecklistData = await dangerousGoodsChecklistModel.save({\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                            tripReport_StopID: data.id,\n                            vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                            bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                            twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                            fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                            noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                            spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                            fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                            dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                            loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                            msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                            anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                            safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                            vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                        });\n                        setDgrChecklist(dgChecklistData);\n                    }));\n                }\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                closeModal();\n            } else {\n                createTripReport_Stop({\n                    variables: {\n                        input: {\n                            ...variables.input,\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createDangerousGoodsChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateDangerousGoodsChecklist, {\n        onCompleted: (response)=>{\n            const data = response.createDangerousGoodsChecklist;\n            setDgrChecklist(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating dangerous goods\", error);\n        }\n    });\n    const [updateDangerousGoodsRecord] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateDangerousGoodsRecord, {\n        onCompleted: (response)=>{\n            const data = response.updateDangerousGoodsRecord;\n            // Process the response\n            (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0 ? getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) : getBufferDgr(data.id);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating dangerous goods record\", error);\n            toast({\n                title: \"Error\",\n                description: \"Error updating dangerous goods record\",\n                variant: \"destructive\"\n            });\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            if (bufferDgr.length > 0) {\n                bufferDgr.map((dgr)=>{\n                    updateDangerousGoodsRecord({\n                        variables: {\n                            input: {\n                                id: dgr.id,\n                                tripReport_StopID: data.id,\n                                type: dgr.type,\n                                comment: dgr.comment\n                            }\n                        }\n                    });\n                    createDangerousGoodsChecklist({\n                        variables: {\n                            input: {\n                                tripReport_StopID: data.id,\n                                vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                                bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                                twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                                fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                                noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                                spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                                fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                                dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                                loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                                msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                                anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                                safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                                vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                            }\n                        }\n                    });\n                });\n            }\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const [updateTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating passenger drop facility\", error);\n        }\n    });\n    const handleCreateNewLocation = async ()=>{\n        const title = document.getElementById(\"new-location-title\");\n        const latitude = document.getElementById(\"new-location-latitude\");\n        const longitude = document.getElementById(\"new-location-longitude\");\n        if (title && latitude && longitude) {\n            if (offline) {\n                // createGeoLocation\n                const data = await geoLocationModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                    title: title.value,\n                    lat: +latitude.value,\n                    long: +longitude.value,\n                    parentID: parentLocation\n                });\n                setLocations([\n                    ...locations,\n                    {\n                        label: data.title,\n                        value: data.id,\n                        latitude: data.lat,\n                        longitude: data.long\n                    }\n                ]);\n                setTripReport_Stops({\n                    ...tripReport_Stops,\n                    geoLocationID: data.id\n                });\n                setOpenNewLocationDialog(false);\n            } else {\n                createGeoLocation({\n                    variables: {\n                        input: {\n                            title: title.value,\n                            lat: +latitude.value,\n                            long: +longitude.value,\n                            parentID: parentLocation\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createGeoLocation] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CREATE_GEO_LOCATION, {\n        onCompleted: (response)=>{\n            const data = response.createGeoLocation;\n            setLocations([\n                ...locations,\n                {\n                    label: data.title,\n                    value: data.id,\n                    latitude: data.lat,\n                    longitude: data.long\n                }\n            ]);\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: data.id\n            });\n            setOpenNewLocationDialog(false);\n        },\n        onError: (error)=>{\n            toast({\n                title: \"Error\",\n                description: \"Error creating GeoLocation: \" + error.message,\n                variant: \"destructive\"\n            });\n            setOpenNewLocationDialog(false);\n            console.error(\"Error creating new location\", error);\n        }\n    });\n    var _tripReport_Stops_depTime;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.P, {\n                className: \"max-w-[40rem] leading-loose\",\n                children: \"For recording trip stops where passengers, cargo and/or vehicles maybe getting on and off.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1020,\n                columnNumber: 13\n            }, this),\n            displayField(type + \"Location\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Location of trip stop\",\n                htmlFor: \"trip-location\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    offline: offline,\n                    setCurrentLocation: setCurrentLocation,\n                    handleLocationChange: handleLocationChangeCallback,\n                    currentEvent: currentEventForLocation\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1029,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1025,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Arrival\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Arrival Time\",\n                htmlFor: \"arrival-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime) || \"\",\n                    buttonLabel: \"Arrive now\",\n                    handleTimeChange: handleArrTimeChange,\n                    timeID: \"arrival-time\",\n                    fieldName: \"Arrival Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1042,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1038,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Departure\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Departure Time\",\n                htmlFor: \"departure-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: depTime ? depTime : (_tripReport_Stops_depTime = tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.depTime) !== null && _tripReport_Stops_depTime !== void 0 ? _tripReport_Stops_depTime : \"\",\n                    buttonLabel: \"Depart now\",\n                    handleTimeChange: handleDepTimeChange,\n                    timeID: \"departure-time\",\n                    fieldName: \"Departure Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1061,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1057,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"PaxPickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers off\",\n                        htmlFor: \"paxOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOff\",\n                            name: \"paxOff\",\n                            type: \"number\",\n                            value: localPaxOff,\n                            placeholder: \"Pax off\",\n                            min: \"0\",\n                            onChange: handlePaxOffChange,\n                            onBlur: handlePaxOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1079,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1075,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers on\",\n                        htmlFor: \"paxOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOn\",\n                            name: \"paxOn\",\n                            type: \"number\",\n                            value: localPaxOn,\n                            placeholder: \"Pax on\",\n                            min: \"0\",\n                            onChange: handlePaxOnChange,\n                            onBlur: handlePaxOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1094,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1090,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1073,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"VehiclePickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles on\",\n                        htmlFor: \"vehicleOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOn\",\n                            name: \"vehicleOn\",\n                            type: \"number\",\n                            value: localVehicleOn,\n                            placeholder: \"Vehicles getting on\",\n                            min: \"0\",\n                            onChange: handleVehicleOnChange,\n                            onBlur: handleVehicleOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1114,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1110,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles off\",\n                        htmlFor: \"vehicleOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOff\",\n                            name: \"vehicleOff\",\n                            type: \"number\",\n                            value: localVehicleOff,\n                            placeholder: \"Vehicles getting off\",\n                            min: \"0\",\n                            onChange: handleVehicleOffChange,\n                            onBlur: handleVehicleOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1129,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1125,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1108,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"OtherCargo\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Cargo (if any)\",\n                htmlFor: \"cargo-onOff\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"cargo-onOff\",\n                    placeholder: \"Other cargo on and off\",\n                    value: cargoOnOff !== \"\" ? cargoOnOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo) || \"\",\n                    onChange: (e)=>{\n                        setCargoOnOff(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            otherCargo: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1147,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1143,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pvpddgr__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                offline: offline,\n                locked: locked,\n                currentTrip: currentTrip,\n                logBookConfig: logBookConfig,\n                selectedDGR: selectedDGR,\n                setSelectedDGR: setSelectedDGR,\n                members: members,\n                displayDangerousGoods: displayDangerousGoods,\n                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                allDangerousGoods: allPVPDDangerousGoods,\n                setAllDangerousGoods: setAllPVPDDangerousGoods,\n                currentEvent: tripEvent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1168,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Comments\",\n                htmlFor: \"comments\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"comments\",\n                    placeholder: \"Comments\",\n                    value: comments !== \"\" ? comments : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments) || \"\",\n                    onChange: (e)=>{\n                        setComments(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            comments: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1191,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1187,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                        onClick: ()=>closeModal(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1211,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"primary\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                        onClick: locked ? ()=>{} : handleSave,\n                        disabled: locked,\n                        children: selectedEvent ? \"Update\" : \"Save\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1217,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1210,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.AlertDialogNew, {\n                openDialog: openNewLocationDialog,\n                setOpenDialog: setOpenNewLocationDialog,\n                actionText: \"Add New Location\",\n                handleCreate: handleCreateNewLocation,\n                title: \"Add New Location\",\n                description: \"Create a new location for trip stops\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-title\",\n                            type: \"text\",\n                            \"aria-describedby\": \"title-error\",\n                            required: true,\n                            placeholder: \"Location Title\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1233,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1232,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__.Combobox, {\n                            options: locations || [],\n                            onChange: handleParentLocationChangeCallback,\n                            placeholder: \"Parent Location (Optional)\",\n                            buttonClassName: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1242,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1241,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-latitude\",\n                            type: \"text\",\n                            defaultValue: location.latitude,\n                            \"aria-describedby\": \"latitude-error\",\n                            required: true,\n                            placeholder: \"Latitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1250,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1249,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-longitude\",\n                            type: \"text\",\n                            defaultValue: location.longitude,\n                            \"aria-describedby\": \"longitude-error\",\n                            required: true,\n                            placeholder: \"Longitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1260,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1259,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1225,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n        lineNumber: 1019,\n        columnNumber: 9\n    }, this);\n}\n_s(PassengerVehiclePickDrop, \"1SZ884mojrQwYbjj1rzEYaLln5k=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation\n    ];\n});\n_c = PassengerVehiclePickDrop;\nvar _c;\n$RefreshReg$(_c, \"PassengerVehiclePickDrop\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx\n"));

/***/ })

});