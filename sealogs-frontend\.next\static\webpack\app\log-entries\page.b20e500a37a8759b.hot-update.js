"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx":
/*!******************************************************************!*\
  !*** ./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PassengerVehiclePickDrop; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _pvpddgr__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../pvpddgr */ \"(app-pages-browser)/./src/app/ui/logbook/pvpddgr.tsx\");\n/* harmony import */ var _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsRecord */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsRecord.js\");\n/* harmony import */ var _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/tripReport_Stop */ \"(app-pages-browser)/./src/app/offline/models/tripReport_Stop.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsChecklist */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsChecklist.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Create model instances outside component to prevent re-instantiation on every render\nconst dangerousGoodsRecordModel = new _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\nconst tripReport_StopModel = new _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\nconst geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\nconst dangerousGoodsChecklistModel = new _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n\n\n\n\n\n\n\nfunction PassengerVehiclePickDrop(param) {\n    let { geoLocations, currentTrip = false, updateTripReport, selectedEvent = false, tripReport, closeModal, type, logBookConfig, members, locked, tripReport_Stops, setTripReport_Stops, displayDangerousGoods = false, displayDangerousGoodsSailing, setDisplayDangerousGoods, setDisplayDangerousGoodsSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGR, setSelectedDGR, offline = false } = param;\n    _s();\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [arrTime, setArrTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [depTime, setDepTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [cargoOnOff, setCargoOnOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(selectedEvent);\n    const [parentLocation, setParentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [bufferDgr, setBufferDgr] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [dgrChecklist, setDgrChecklist] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [tripEvent, setTripEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [openNewLocationDialog, setOpenNewLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // State for LocationField to track current location selection\n    const [currentEventForLocation, setCurrentEventForLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        geoLocationID: 0,\n        lat: null,\n        long: null\n    });\n    // Local state for input values to prevent focus loss\n    const [localPaxOn, setLocalPaxOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localPaxOff, setLocalPaxOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOn, setLocalVehicleOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOff, setLocalVehicleOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // Memoize displayField results to prevent re-computation on every render\n    const displayFieldResults = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents;\n        const eventTypesConfig = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        const fieldMap = new Map();\n        if ((eventTypesConfig === null || eventTypesConfig === void 0 ? void 0 : eventTypesConfig.length) > 0) {\n            var _eventTypesConfig__customisedComponentFields, _eventTypesConfig_;\n            (_eventTypesConfig_ = eventTypesConfig[0]) === null || _eventTypesConfig_ === void 0 ? void 0 : (_eventTypesConfig__customisedComponentFields = _eventTypesConfig_.customisedComponentFields) === null || _eventTypesConfig__customisedComponentFields === void 0 ? void 0 : _eventTypesConfig__customisedComponentFields.nodes.forEach((field)=>{\n                if (field.status !== \"Off\") {\n                    fieldMap.set(field.fieldName, true);\n                }\n            });\n        }\n        return fieldMap;\n    }, [\n        logBookConfig\n    ]);\n    const displayField = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((fieldName)=>{\n        return displayFieldResults.get(fieldName) || false;\n    }, [\n        displayFieldResults\n    ]);\n    // Stable onChange handlers that only update local state\n    const handlePaxOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOff(e.target.value);\n    }, []);\n    const handlePaxOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOn(e.target.value);\n    }, []);\n    const handleVehicleOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOn(e.target.value);\n    }, []);\n    const handleVehicleOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOff(e.target.value);\n    }, []);\n    // Stable onBlur handlers that update the main state\n    const handlePaxOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handlePaxOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    // Helper functions for location handling\n    const clearLocationData = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDDFA️ Clearing location\");\n        const clearedData = {\n            geoLocationID: 0,\n            lat: null,\n            long: null\n        };\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                ...clearedData\n            }));\n        setCurrentEventForLocation(clearedData);\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleNewLocationRequest = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (\"geolocation\" in navigator) {\n            navigator.geolocation.getCurrentPosition((param)=>{\n                let { coords } = param;\n                const { latitude, longitude } = coords;\n                setLocation({\n                    latitude,\n                    longitude\n                });\n                setOpenNewLocationDialog(true);\n            });\n        } else {\n            toast({\n                title: \"Error\",\n                description: \"Geolocation is not supported by your browser\",\n                variant: \"destructive\"\n            });\n            setOpenNewLocationDialog(true);\n        }\n    }, [\n        setLocation,\n        setOpenNewLocationDialog,\n        toast\n    ]);\n    const handleLocationSelection = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        console.log(\"\\uD83D\\uDDFA️ Location selected:\", value.label, \"ID:\", value.value);\n        const locationData = {\n            geoLocationID: value.value,\n            lat: value.latitude,\n            long: value.longitude\n        };\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                ...locationData\n            }));\n        setCurrentEventForLocation(locationData);\n        setLocation({\n            latitude: value.latitude,\n            longitude: value.longitude\n        });\n    }, [\n        setTripReport_Stops,\n        setLocation\n    ]);\n    const handleCoordinatesInput = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        console.log(\"\\uD83D\\uDDFA️ Coordinates entered:\", value.latitude, value.longitude);\n        const coordinatesData = {\n            geoLocationID: 0,\n            lat: value.latitude,\n            long: value.longitude\n        };\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                ...coordinatesData\n            }));\n        setCurrentEventForLocation(coordinatesData);\n        setLocation({\n            latitude: value.latitude,\n            longitude: value.longitude\n        });\n    }, [\n        setTripReport_Stops,\n        setLocation\n    ]);\n    // Memoize main callback function\n    const handleLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        console.log(\"\\uD83D\\uDDFA️ LocationChange:\", value);\n        // If value is null or undefined, clear the location\n        if (!value) {\n            clearLocationData();\n            return;\n        }\n        // Handle \"Add new location\" option\n        if (value.value === \"newLocation\") {\n            handleNewLocationRequest();\n            return;\n        }\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // If the value object has latitude and longitude, handle location selection\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                handleLocationSelection(value);\n            }\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            handleCoordinatesInput(value);\n        }\n    }, [\n        clearLocationData,\n        handleNewLocationRequest,\n        handleLocationSelection,\n        handleCoordinatesInput\n    ]);\n    const handleParentLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((selectedLocation)=>{\n        setParentLocation((selectedLocation === null || selectedLocation === void 0 ? void 0 : selectedLocation.value) || null);\n    }, [\n        setParentLocation\n    ]);\n    const handleArrTimeChange = (date)=>{\n        const formattedTime = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\");\n        setArrTime(formattedTime);\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            arriveTime: formattedTime,\n            arrTime: formattedTime\n        });\n    };\n    const handleDepTimeChange = (date)=>{\n        setDepTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            depTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"),\n            departTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\")\n        });\n    };\n    // Initialize local state from tripReport_Stops\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (tripReport_Stops) {\n            var _tripReport_Stops_paxOn, _tripReport_Stops_paxOff, _tripReport_Stops_vehicleOn, _tripReport_Stops_vehicleOff;\n            setLocalPaxOn(((_tripReport_Stops_paxOn = tripReport_Stops.paxOn) === null || _tripReport_Stops_paxOn === void 0 ? void 0 : _tripReport_Stops_paxOn.toString()) || \"\");\n            setLocalPaxOff(((_tripReport_Stops_paxOff = tripReport_Stops.paxOff) === null || _tripReport_Stops_paxOff === void 0 ? void 0 : _tripReport_Stops_paxOff.toString()) || \"\");\n            setLocalVehicleOn(((_tripReport_Stops_vehicleOn = tripReport_Stops.vehicleOn) === null || _tripReport_Stops_vehicleOn === void 0 ? void 0 : _tripReport_Stops_vehicleOn.toString()) || \"\");\n            setLocalVehicleOff(((_tripReport_Stops_vehicleOff = tripReport_Stops.vehicleOff) === null || _tripReport_Stops_vehicleOff === void 0 ? void 0 : _tripReport_Stops_vehicleOff.toString()) || \"\");\n        }\n    }, [\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff\n    ]);\n    // Only initialize from existing records when editing (no automatic sync)\n    const [hasInitialized, setHasInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!hasInitialized && selectedEvent && tripReport_Stops && (tripReport_Stops.geoLocationID || tripReport_Stops.lat || tripReport_Stops.long)) {\n            console.log(\"\\uD83D\\uDDFA️ One-time init from existing record:\", {\n                geoLocationID: tripReport_Stops.geoLocationID,\n                lat: tripReport_Stops.lat,\n                long: tripReport_Stops.long\n            });\n            setCurrentEventForLocation({\n                geoLocationID: tripReport_Stops.geoLocationID || 0,\n                lat: tripReport_Stops.lat || null,\n                long: tripReport_Stops.long || null\n            });\n            setHasInitialized(true);\n        }\n    }, [\n        selectedEvent,\n        hasInitialized\n    ]) // Removed tripReport_Stops dependencies to prevent re-runs\n    ;\n    // Track currentEventForLocation changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDDFA️ currentEventForLocation updated:\", currentEventForLocation);\n    }, [\n        currentEventForLocation\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentTripReport_Stop(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        }\n    }, [\n        selectedEvent\n    ]);\n    const offlineCreateDangerousGoodsChecklist = async ()=>{\n        // createDangerousGoodsChecklist\n        const delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\n        await delay(2000);\n        const data = await dangerousGoodsChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n        });\n        setDgrChecklist(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (currentEvent) {\n            getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            setDgrChecklist(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.dangerousGoodsChecklist);\n        } else {\n            // Initialize default values for new records\n            if (!tripReport_Stops) {\n                setTripReport_Stops({\n                    paxOn: 0,\n                    paxOff: 0,\n                    vehicleOn: 0,\n                    vehicleOff: 0\n                });\n            }\n        }\n    }, [\n        currentEvent\n    ]);\n    const getCurrentTripReport_Stop = async (id)=>{\n        if (offline) {\n            // tripReport_Stop\n            const event = await tripReport_StopModel.getById(id);\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3, _event_stopLocation4, _event_stopLocation5;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long\n                    });\n                    // Initialize currentEventForLocation for LocationField\n                    const locationData = {\n                        geoLocationID: event.stopLocationID || 0,\n                        lat: ((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) || (event === null || event === void 0 ? void 0 : event.lat) || null,\n                        long: ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long) || (event === null || event === void 0 ? void 0 : event.long) || null\n                    };\n                    console.log(\"\\uD83D\\uDDFA️ Loading offline data:\", locationData);\n                    setCurrentEventForLocation(locationData);\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat) && ((_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long)) {\n                        var _event_stopLocation6, _event_stopLocation7;\n                        console.log(\"\\uD83D\\uDDFA️ Stop location:\", event);\n                        setLocation({\n                            latitude: (_event_stopLocation6 = event.stopLocation) === null || _event_stopLocation6 === void 0 ? void 0 : _event_stopLocation6.lat,\n                            longitude: (_event_stopLocation7 = event.stopLocation) === null || _event_stopLocation7 === void 0 ? void 0 : _event_stopLocation7.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        console.log(\"\\uD83D\\uDDFA️ Stop lat long:\", event);\n                        setLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        } else {\n            tripReport_Stop({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const getBufferDgr = async (id)=>{\n        if (bufferDgr.length > 0) {\n            const dgr = bufferDgr.map((d)=>{\n                return +d.id;\n            });\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    ...dgr,\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            ...dgr,\n                            +id\n                        ]\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            +id\n                        ]\n                    }\n                });\n            }\n        }\n    };\n    const [getDgrList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetDangerousGoodsRecords, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setBufferDgr(data.readDangerousGoodsRecords.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error getting buffer dgr\", error);\n        }\n    });\n    const [tripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetTripReport_Stop, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripReport_Stop;\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setDisplayDangerousGoodsSailing(displayDangerousGoodsSailing !== null ? displayDangerousGoodsSailing : event === null || event === void 0 ? void 0 : event.designatedDangerousGoodsSailing);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3, _event_stopLocation4, _event_stopLocation5;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long,\n                        designatedDangerousGoodsSailing: event.designatedDangerousGoodsSailing\n                    });\n                    // Initialize currentEventForLocation for LocationField\n                    const locationData = {\n                        geoLocationID: event.stopLocationID || 0,\n                        lat: ((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) || (event === null || event === void 0 ? void 0 : event.lat) || null,\n                        long: ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long) || (event === null || event === void 0 ? void 0 : event.long) || null\n                    };\n                    console.log(\"\\uD83D\\uDDFA️ Loading online data:\", locationData);\n                    setCurrentEventForLocation(locationData);\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat) && ((_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long)) {\n                        var _event_stopLocation6, _event_stopLocation7;\n                        console.log(\"\\uD83D\\uDDFA️ stopLocation trip:\", event);\n                        setLocation({\n                            latitude: (_event_stopLocation6 = event.stopLocation) === null || _event_stopLocation6 === void 0 ? void 0 : _event_stopLocation6.lat,\n                            longitude: (_event_stopLocation7 = event.stopLocation) === null || _event_stopLocation7 === void 0 ? void 0 : _event_stopLocation7.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        console.log(\"\\uD83D\\uDDFA️ Stop lat long trip:\", event);\n                        setLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    // Memoize locations array to prevent unnecessary re-creation\n    const memoizedLocations = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (!geoLocations) return [];\n        return [\n            {\n                label: \"--- Add new location ---\",\n                value: \"newLocation\"\n            },\n            ...geoLocations.filter((location)=>location.title).map((location)=>({\n                    label: location.title,\n                    value: location.id,\n                    latitude: location.lat,\n                    longitude: location.long\n                }))\n        ];\n    }, [\n        geoLocations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setLocations(memoizedLocations);\n    }, [\n        memoizedLocations\n    ]);\n    const validateForm = ()=>{\n        // Validate stopLocationID\n        const stopLocationID = +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID);\n        if (!stopLocationID || stopLocationID <= 0) {\n            toast({\n                title: \"Error\",\n                description: \"Please select a trip stop location\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        // Validate arriveTime - check both arrTime and tripReport_Stops.arriveTime/arrTime\n        const arriveTimeValue = arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime);\n        // Use isEmpty but also check for false value since arrTime's initial state is false\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(arriveTimeValue) || arriveTimeValue === false) {\n            toast({\n                title: \"Error\",\n                description: \"Please enter an arrival time\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        return true;\n    };\n    console.log(\"location\", location);\n    const handleSave = async ()=>{\n        // Validate form before saving\n        if (!validateForm()) {\n            return;\n        }\n        // Use local state values for the most current input data\n        const paxOnValue = localPaxOn !== \"\" ? +localPaxOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn) || 0;\n        const paxOffValue = localPaxOff !== \"\" ? +localPaxOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff) || 0;\n        const vehicleOnValue = localVehicleOn !== \"\" ? +localVehicleOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn) || 0;\n        const vehicleOffValue = localVehicleOff !== \"\" ? +localVehicleOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff) || 0;\n        const variables = {\n            input: {\n                arriveTime: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime),\n                departTime: depTime ? depTime : tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.departTime,\n                paxJoined: paxOnValue,\n                paxDeparted: paxOffValue,\n                vehiclesJoined: isNaN(vehicleOnValue) ? 0 : vehicleOnValue,\n                vehiclesDeparted: isNaN(vehicleOffValue) ? 0 : vehicleOffValue,\n                stopLocationID: +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID),\n                otherCargo: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo,\n                comments: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments,\n                lat: location.latitude.toString(),\n                long: location.longitude.toString(),\n                dangerousGoodsChecklistID: +(dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.id),\n                designatedDangerousGoodsSailing: displayDangerousGoodsSailing\n            }\n        };\n        if (currentEvent) {\n            if (offline) {\n                // updateTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                    ...variables.input\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n            } else {\n                updateTripReport_Stop({\n                    variables: {\n                        input: {\n                            id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            // Set default values for new records\n            variables.input.paxJoined = variables.input.paxJoined || 0;\n            variables.input.paxDeparted = variables.input.paxDeparted || 0;\n            if (offline) {\n                // createTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    ...variables.input,\n                    logBookEntrySectionID: currentTrip.id,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                if (bufferDgr.length > 0) {\n                    Promise.all(bufferDgr.map(async (dgr)=>{\n                        // updateDangerousGoodsRecord\n                        const dgrData = await dangerousGoodsRecordModel.save({\n                            id: dgr.id,\n                            tripReport_StopID: data.id,\n                            type: dgr.type,\n                            comment: dgr.comment\n                        });\n                        // Clear any existing toasts\n                        if ((currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0) {\n                            await getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                        } else {\n                            if (dgrData) {\n                                await getBufferDgr(dgrData.id);\n                            }\n                        }\n                        // createDangerousGoodsChecklist\n                        const dgChecklistData = await dangerousGoodsChecklistModel.save({\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                            tripReport_StopID: data.id,\n                            vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                            bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                            twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                            fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                            noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                            spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                            fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                            dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                            loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                            msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                            anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                            safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                            vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                        });\n                        setDgrChecklist(dgChecklistData);\n                    }));\n                }\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                closeModal();\n            } else {\n                createTripReport_Stop({\n                    variables: {\n                        input: {\n                            ...variables.input,\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createDangerousGoodsChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateDangerousGoodsChecklist, {\n        onCompleted: (response)=>{\n            const data = response.createDangerousGoodsChecklist;\n            setDgrChecklist(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating dangerous goods\", error);\n        }\n    });\n    const [updateDangerousGoodsRecord] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateDangerousGoodsRecord, {\n        onCompleted: (response)=>{\n            const data = response.updateDangerousGoodsRecord;\n            // Process the response\n            (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0 ? getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) : getBufferDgr(data.id);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating dangerous goods record\", error);\n            toast({\n                title: \"Error\",\n                description: \"Error updating dangerous goods record\",\n                variant: \"destructive\"\n            });\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            if (bufferDgr.length > 0) {\n                bufferDgr.map((dgr)=>{\n                    updateDangerousGoodsRecord({\n                        variables: {\n                            input: {\n                                id: dgr.id,\n                                tripReport_StopID: data.id,\n                                type: dgr.type,\n                                comment: dgr.comment\n                            }\n                        }\n                    });\n                    createDangerousGoodsChecklist({\n                        variables: {\n                            input: {\n                                tripReport_StopID: data.id,\n                                vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                                bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                                twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                                fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                                noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                                spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                                fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                                dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                                loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                                msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                                anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                                safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                                vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                            }\n                        }\n                    });\n                });\n            }\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const [updateTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating passenger drop facility\", error);\n        }\n    });\n    const handleCreateNewLocation = async ()=>{\n        const title = document.getElementById(\"new-location-title\");\n        const latitude = document.getElementById(\"new-location-latitude\");\n        const longitude = document.getElementById(\"new-location-longitude\");\n        if (title && latitude && longitude) {\n            if (offline) {\n                // createGeoLocation\n                const data = await geoLocationModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                    title: title.value,\n                    lat: +latitude.value,\n                    long: +longitude.value,\n                    parentID: parentLocation\n                });\n                setLocations([\n                    ...locations,\n                    {\n                        label: data.title,\n                        value: data.id,\n                        latitude: data.lat,\n                        longitude: data.long\n                    }\n                ]);\n                setTripReport_Stops({\n                    ...tripReport_Stops,\n                    geoLocationID: data.id\n                });\n                setOpenNewLocationDialog(false);\n            } else {\n                createGeoLocation({\n                    variables: {\n                        input: {\n                            title: title.value,\n                            lat: +latitude.value,\n                            long: +longitude.value,\n                            parentID: parentLocation\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createGeoLocation] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CREATE_GEO_LOCATION, {\n        onCompleted: (response)=>{\n            const data = response.createGeoLocation;\n            setLocations([\n                ...locations,\n                {\n                    label: data.title,\n                    value: data.id,\n                    latitude: data.lat,\n                    longitude: data.long\n                }\n            ]);\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: data.id\n            });\n            setOpenNewLocationDialog(false);\n        },\n        onError: (error)=>{\n            toast({\n                title: \"Error\",\n                description: \"Error creating GeoLocation: \" + error.message,\n                variant: \"destructive\"\n            });\n            setOpenNewLocationDialog(false);\n            console.error(\"Error creating new location\", error);\n        }\n    });\n    var _tripReport_Stops_depTime;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.P, {\n                className: \"max-w-[40rem] leading-loose\",\n                children: \"For recording trip stops where passengers, cargo and/or vehicles maybe getting on and off.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1030,\n                columnNumber: 13\n            }, this),\n            displayField(type + \"Location\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Location of trip stop\",\n                htmlFor: \"trip-location\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    offline: offline,\n                    setCurrentLocation: setLocation,\n                    handleLocationChange: handleLocationChangeCallback,\n                    currentEvent: currentEventForLocation,\n                    showAddNewLocation: true,\n                    showUseCoordinates: true,\n                    showCurrentLocation: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1039,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1035,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Arrival\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Arrival Time\",\n                htmlFor: \"arrival-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime) || \"\",\n                    buttonLabel: \"Arrive now\",\n                    handleTimeChange: handleArrTimeChange,\n                    timeID: \"arrival-time\",\n                    fieldName: \"Arrival Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1055,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1051,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Departure\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Departure Time\",\n                htmlFor: \"departure-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: depTime ? depTime : (_tripReport_Stops_depTime = tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.depTime) !== null && _tripReport_Stops_depTime !== void 0 ? _tripReport_Stops_depTime : \"\",\n                    buttonLabel: \"Depart now\",\n                    handleTimeChange: handleDepTimeChange,\n                    timeID: \"departure-time\",\n                    fieldName: \"Departure Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1074,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1070,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"PaxPickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers off\",\n                        htmlFor: \"paxOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOff\",\n                            name: \"paxOff\",\n                            type: \"number\",\n                            value: localPaxOff,\n                            placeholder: \"Pax off\",\n                            min: \"0\",\n                            onChange: handlePaxOffChange,\n                            onBlur: handlePaxOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1092,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1088,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers on\",\n                        htmlFor: \"paxOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOn\",\n                            name: \"paxOn\",\n                            type: \"number\",\n                            value: localPaxOn,\n                            placeholder: \"Pax on\",\n                            min: \"0\",\n                            onChange: handlePaxOnChange,\n                            onBlur: handlePaxOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1107,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1103,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1086,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"VehiclePickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles on\",\n                        htmlFor: \"vehicleOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOn\",\n                            name: \"vehicleOn\",\n                            type: \"number\",\n                            value: localVehicleOn,\n                            placeholder: \"Vehicles getting on\",\n                            min: \"0\",\n                            onChange: handleVehicleOnChange,\n                            onBlur: handleVehicleOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1127,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1123,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles off\",\n                        htmlFor: \"vehicleOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOff\",\n                            name: \"vehicleOff\",\n                            type: \"number\",\n                            value: localVehicleOff,\n                            placeholder: \"Vehicles getting off\",\n                            min: \"0\",\n                            onChange: handleVehicleOffChange,\n                            onBlur: handleVehicleOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1142,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1138,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1121,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"OtherCargo\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Cargo (if any)\",\n                htmlFor: \"cargo-onOff\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"cargo-onOff\",\n                    placeholder: \"Other cargo on and off\",\n                    value: cargoOnOff !== \"\" ? cargoOnOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo) || \"\",\n                    onChange: (e)=>{\n                        setCargoOnOff(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            otherCargo: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1160,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1156,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pvpddgr__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                offline: offline,\n                locked: locked,\n                currentTrip: currentTrip,\n                logBookConfig: logBookConfig,\n                selectedDGR: selectedDGR,\n                setSelectedDGR: setSelectedDGR,\n                members: members,\n                displayDangerousGoods: displayDangerousGoods,\n                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                allDangerousGoods: allPVPDDangerousGoods,\n                setAllDangerousGoods: setAllPVPDDangerousGoods,\n                currentEvent: tripEvent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1181,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Comments\",\n                htmlFor: \"comments\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"comments\",\n                    placeholder: \"Comments\",\n                    value: comments !== \"\" ? comments : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments) || \"\",\n                    onChange: (e)=>{\n                        setComments(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            comments: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1204,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1200,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                        onClick: ()=>closeModal(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1224,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"primary\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                        onClick: locked ? ()=>{} : handleSave,\n                        disabled: locked,\n                        children: selectedEvent ? \"Update\" : \"Save\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1230,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1223,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.AlertDialogNew, {\n                openDialog: openNewLocationDialog,\n                setOpenDialog: setOpenNewLocationDialog,\n                actionText: \"Add New Location\",\n                handleCreate: handleCreateNewLocation,\n                title: \"Add New Location\",\n                description: \"Create a new location for trip stops\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-title\",\n                            type: \"text\",\n                            \"aria-describedby\": \"title-error\",\n                            required: true,\n                            placeholder: \"Location Title\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1246,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1245,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__.Combobox, {\n                            options: locations || [],\n                            onChange: handleParentLocationChangeCallback,\n                            placeholder: \"Parent Location (Optional)\",\n                            buttonClassName: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1255,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1254,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-latitude\",\n                            type: \"text\",\n                            defaultValue: location.latitude,\n                            \"aria-describedby\": \"latitude-error\",\n                            required: true,\n                            placeholder: \"Latitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1263,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1262,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-longitude\",\n                            type: \"text\",\n                            defaultValue: location.longitude,\n                            \"aria-describedby\": \"longitude-error\",\n                            required: true,\n                            placeholder: \"Longitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1273,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1272,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1238,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n        lineNumber: 1029,\n        columnNumber: 9\n    }, this);\n}\n_s(PassengerVehiclePickDrop, \"IDM8BP06AR1hXRVCz9AohpcZ2x4=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation\n    ];\n});\n_c = PassengerVehiclePickDrop;\nvar _c;\n$RefreshReg$(_c, \"PassengerVehiclePickDrop\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx\n"));

/***/ })

});