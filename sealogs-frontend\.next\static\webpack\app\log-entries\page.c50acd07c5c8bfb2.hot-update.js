"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx":
/*!******************************************************************!*\
  !*** ./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PassengerVehiclePickDrop; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _pvpddgr__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../pvpddgr */ \"(app-pages-browser)/./src/app/ui/logbook/pvpddgr.tsx\");\n/* harmony import */ var _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsRecord */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsRecord.js\");\n/* harmony import */ var _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/tripReport_Stop */ \"(app-pages-browser)/./src/app/offline/models/tripReport_Stop.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsChecklist */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsChecklist.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Create model instances outside component to prevent re-instantiation on every render\nconst dangerousGoodsRecordModel = new _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\nconst tripReport_StopModel = new _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\nconst geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\nconst dangerousGoodsChecklistModel = new _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n\n\n\n\n\n\n\nfunction PassengerVehiclePickDrop(param) {\n    let { geoLocations, currentTrip = false, updateTripReport, selectedEvent = false, tripReport, closeModal, type, logBookConfig, members, locked, tripReport_Stops, setTripReport_Stops, displayDangerousGoods = false, displayDangerousGoodsSailing, setDisplayDangerousGoods, setDisplayDangerousGoodsSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGR, setSelectedDGR, offline = false } = param;\n    _s();\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [arrTime, setArrTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [depTime, setDepTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [cargoOnOff, setCargoOnOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(selectedEvent);\n    const [parentLocation, setParentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [bufferDgr, setBufferDgr] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [dgrChecklist, setDgrChecklist] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [tripEvent, setTripEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [openNewLocationDialog, setOpenNewLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // State for LocationField to track current location selection\n    const [currentEventForLocation, setCurrentEventForLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        geoLocationID: 0,\n        lat: null,\n        long: null\n    });\n    // Local state for input values to prevent focus loss\n    const [localPaxOn, setLocalPaxOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localPaxOff, setLocalPaxOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOn, setLocalVehicleOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOff, setLocalVehicleOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // Memoize displayField results to prevent re-computation on every render\n    const displayFieldResults = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents;\n        const eventTypesConfig = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        const fieldMap = new Map();\n        if ((eventTypesConfig === null || eventTypesConfig === void 0 ? void 0 : eventTypesConfig.length) > 0) {\n            var _eventTypesConfig__customisedComponentFields, _eventTypesConfig_;\n            (_eventTypesConfig_ = eventTypesConfig[0]) === null || _eventTypesConfig_ === void 0 ? void 0 : (_eventTypesConfig__customisedComponentFields = _eventTypesConfig_.customisedComponentFields) === null || _eventTypesConfig__customisedComponentFields === void 0 ? void 0 : _eventTypesConfig__customisedComponentFields.nodes.forEach((field)=>{\n                if (field.status !== \"Off\") {\n                    fieldMap.set(field.fieldName, true);\n                }\n            });\n        }\n        return fieldMap;\n    }, [\n        logBookConfig\n    ]);\n    const displayField = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((fieldName)=>{\n        return displayFieldResults.get(fieldName) || false;\n    }, [\n        displayFieldResults\n    ]);\n    // Stable onChange handlers that only update local state\n    const handlePaxOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOff(e.target.value);\n    }, []);\n    const handlePaxOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOn(e.target.value);\n    }, []);\n    const handleVehicleOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOn(e.target.value);\n    }, []);\n    const handleVehicleOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOff(e.target.value);\n    }, []);\n    // Stable onBlur handlers that update the main state\n    const handlePaxOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handlePaxOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    // Helper functions for location handling\n    const clearLocationData = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDDFA️ Clearing location\");\n        const clearedData = {\n            geoLocationID: 0,\n            lat: null,\n            long: null\n        };\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                ...clearedData\n            }));\n        setCurrentEventForLocation(clearedData);\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleNewLocationRequest = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (\"geolocation\" in navigator) {\n            navigator.geolocation.getCurrentPosition((param)=>{\n                let { coords } = param;\n                const { latitude, longitude } = coords;\n                setLocation({\n                    latitude,\n                    longitude\n                });\n                setOpenNewLocationDialog(true);\n            });\n        } else {\n            toast({\n                title: \"Error\",\n                description: \"Geolocation is not supported by your browser\",\n                variant: \"destructive\"\n            });\n            setOpenNewLocationDialog(true);\n        }\n    }, [\n        setLocation,\n        setOpenNewLocationDialog,\n        toast\n    ]);\n    const handleLocationSelection = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        console.log(\"\\uD83D\\uDDFA️ Location selected:\", value.label, \"ID:\", value.value);\n        const locationData = {\n            geoLocationID: value.value,\n            lat: value.latitude,\n            long: value.longitude\n        };\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                ...locationData\n            }));\n        setCurrentEventForLocation(locationData);\n        setLocation({\n            latitude: value.latitude,\n            longitude: value.longitude\n        });\n    }, [\n        setTripReport_Stops,\n        setLocation\n    ]);\n    const handleCoordinatesInput = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        console.log(\"\\uD83D\\uDDFA️ Coordinates entered:\", value.latitude, value.longitude);\n        const coordinatesData = {\n            geoLocationID: 0,\n            lat: value.latitude,\n            long: value.longitude\n        };\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                ...coordinatesData\n            }));\n        setCurrentEventForLocation(coordinatesData);\n        setLocation({\n            latitude: value.latitude,\n            longitude: value.longitude\n        });\n    }, [\n        setTripReport_Stops,\n        setLocation\n    ]);\n    // Memoize main callback function\n    const handleLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        console.log(\"\\uD83D\\uDDFA️ LocationChange:\", value);\n        // If value is null or undefined, clear the location\n        if (!value) {\n            clearLocationData();\n            return;\n        }\n        // Handle \"Add new location\" option\n        if (value.value === \"newLocation\") {\n            handleNewLocationRequest();\n            return;\n        }\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // If the value object has latitude and longitude, handle location selection\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                handleLocationSelection(value);\n            }\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            handleCoordinatesInput(value);\n        }\n    }, [\n        clearLocationData,\n        handleNewLocationRequest,\n        handleLocationSelection,\n        handleCoordinatesInput\n    ]);\n    const handleParentLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((selectedLocation)=>{\n        setParentLocation((selectedLocation === null || selectedLocation === void 0 ? void 0 : selectedLocation.value) || null);\n    }, [\n        setParentLocation\n    ]);\n    const handleArrTimeChange = (date)=>{\n        const formattedTime = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\");\n        setArrTime(formattedTime);\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            arriveTime: formattedTime,\n            arrTime: formattedTime\n        });\n    };\n    const handleDepTimeChange = (date)=>{\n        setDepTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            depTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"),\n            departTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\")\n        });\n    };\n    // Initialize local state from tripReport_Stops\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (tripReport_Stops) {\n            var _tripReport_Stops_paxOn, _tripReport_Stops_paxOff, _tripReport_Stops_vehicleOn, _tripReport_Stops_vehicleOff;\n            setLocalPaxOn(((_tripReport_Stops_paxOn = tripReport_Stops.paxOn) === null || _tripReport_Stops_paxOn === void 0 ? void 0 : _tripReport_Stops_paxOn.toString()) || \"\");\n            setLocalPaxOff(((_tripReport_Stops_paxOff = tripReport_Stops.paxOff) === null || _tripReport_Stops_paxOff === void 0 ? void 0 : _tripReport_Stops_paxOff.toString()) || \"\");\n            setLocalVehicleOn(((_tripReport_Stops_vehicleOn = tripReport_Stops.vehicleOn) === null || _tripReport_Stops_vehicleOn === void 0 ? void 0 : _tripReport_Stops_vehicleOn.toString()) || \"\");\n            setLocalVehicleOff(((_tripReport_Stops_vehicleOff = tripReport_Stops.vehicleOff) === null || _tripReport_Stops_vehicleOff === void 0 ? void 0 : _tripReport_Stops_vehicleOff.toString()) || \"\");\n        }\n    }, [\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff\n    ]);\n    // Only initialize from existing records when editing (no automatic sync)\n    const [hasInitialized, setHasInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!hasInitialized && selectedEvent && tripReport_Stops && (tripReport_Stops.geoLocationID || tripReport_Stops.lat || tripReport_Stops.long)) {\n            console.log(\"\\uD83D\\uDDFA️ One-time init from existing record:\", {\n                geoLocationID: tripReport_Stops.geoLocationID,\n                lat: tripReport_Stops.lat,\n                long: tripReport_Stops.long\n            });\n            setCurrentEventForLocation({\n                geoLocationID: tripReport_Stops.geoLocationID || 0,\n                lat: tripReport_Stops.lat || null,\n                long: tripReport_Stops.long || null\n            });\n            setHasInitialized(true);\n        }\n    }, [\n        selectedEvent,\n        hasInitialized\n    ]) // Removed tripReport_Stops dependencies to prevent re-runs\n    ;\n    // Track currentEventForLocation changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDDFA️ currentEventForLocation updated:\", currentEventForLocation);\n    }, [\n        currentEventForLocation\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentTripReport_Stop(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        }\n    }, [\n        selectedEvent\n    ]);\n    const offlineCreateDangerousGoodsChecklist = async ()=>{\n        // createDangerousGoodsChecklist\n        const delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\n        await delay(2000);\n        const data = await dangerousGoodsChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n        });\n        setDgrChecklist(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (currentEvent) {\n            getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            setDgrChecklist(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.dangerousGoodsChecklist);\n        } else {\n            // Initialize default values for new records\n            if (!tripReport_Stops) {\n                setTripReport_Stops({\n                    paxOn: 0,\n                    paxOff: 0,\n                    vehicleOn: 0,\n                    vehicleOff: 0\n                });\n            }\n        }\n    }, [\n        currentEvent\n    ]);\n    const getCurrentTripReport_Stop = async (id)=>{\n        if (offline) {\n            // tripReport_Stop\n            const event = await tripReport_StopModel.getById(id);\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3, _event_stopLocation4, _event_stopLocation5;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long\n                    });\n                    // Initialize currentEventForLocation for LocationField\n                    const locationData = {\n                        geoLocationID: event.stopLocationID || 0,\n                        lat: ((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) || (event === null || event === void 0 ? void 0 : event.lat) || null,\n                        long: ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long) || (event === null || event === void 0 ? void 0 : event.long) || null\n                    };\n                    console.log(\"\\uD83D\\uDDFA️ Loading offline data:\", locationData);\n                    setCurrentEventForLocation(locationData);\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat) && ((_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long)) {\n                        var _event_stopLocation6, _event_stopLocation7;\n                        console.log(\"\\uD83D\\uDDFA️ Stop location:\", event);\n                        setLocation({\n                            latitude: (_event_stopLocation6 = event.stopLocation) === null || _event_stopLocation6 === void 0 ? void 0 : _event_stopLocation6.lat,\n                            longitude: (_event_stopLocation7 = event.stopLocation) === null || _event_stopLocation7 === void 0 ? void 0 : _event_stopLocation7.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        console.log(\"\\uD83D\\uDDFA️ Stop lat long:\", event);\n                        setLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        } else {\n            tripReport_Stop({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const getBufferDgr = async (id)=>{\n        if (bufferDgr.length > 0) {\n            const dgr = bufferDgr.map((d)=>{\n                return +d.id;\n            });\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    ...dgr,\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            ...dgr,\n                            +id\n                        ]\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            +id\n                        ]\n                    }\n                });\n            }\n        }\n    };\n    const [getDgrList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetDangerousGoodsRecords, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setBufferDgr(data.readDangerousGoodsRecords.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error getting buffer dgr\", error);\n        }\n    });\n    const [tripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetTripReport_Stop, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripReport_Stop;\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setDisplayDangerousGoodsSailing(displayDangerousGoodsSailing !== null ? displayDangerousGoodsSailing : event === null || event === void 0 ? void 0 : event.designatedDangerousGoodsSailing);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3, _event_stopLocation4, _event_stopLocation5;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long,\n                        designatedDangerousGoodsSailing: event.designatedDangerousGoodsSailing\n                    });\n                    // Initialize currentEventForLocation for LocationField\n                    const locationData = {\n                        geoLocationID: event.stopLocationID || 0,\n                        lat: ((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) || (event === null || event === void 0 ? void 0 : event.lat) || null,\n                        long: ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long) || (event === null || event === void 0 ? void 0 : event.long) || null\n                    };\n                    console.log(\"\\uD83D\\uDDFA️ Loading online data:\", locationData);\n                    setCurrentEventForLocation(locationData);\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat) && ((_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long)) {\n                        var _event_stopLocation6, _event_stopLocation7;\n                        console.log(\"\\uD83D\\uDDFA️ stopLocation trip:\", event);\n                        setLocation({\n                            latitude: (_event_stopLocation6 = event.stopLocation) === null || _event_stopLocation6 === void 0 ? void 0 : _event_stopLocation6.lat,\n                            longitude: (_event_stopLocation7 = event.stopLocation) === null || _event_stopLocation7 === void 0 ? void 0 : _event_stopLocation7.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        console.log(\"\\uD83D\\uDDFA️ Stop lat long trip:\", event);\n                        setLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    // Memoize locations array to prevent unnecessary re-creation\n    const memoizedLocations = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (!geoLocations) return [];\n        return [\n            {\n                label: \"--- Add new location ---\",\n                value: \"newLocation\"\n            },\n            ...geoLocations.filter((location)=>location.title).map((location)=>({\n                    label: location.title,\n                    value: location.id,\n                    latitude: location.lat,\n                    longitude: location.long\n                }))\n        ];\n    }, [\n        geoLocations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setLocations(memoizedLocations);\n    }, [\n        memoizedLocations\n    ]);\n    // Synchronize currentEventForLocation with tripReport_Stops\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (tripReport_Stops) {\n            setCurrentEventForLocation({\n                geoLocationID: tripReport_Stops.geoLocationID || 0,\n                lat: tripReport_Stops.lat,\n                long: tripReport_Stops.long\n            });\n        }\n    }, [\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.lat,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.long\n    ]);\n    const validateForm = ()=>{\n        // Validate stopLocationID\n        const stopLocationID = +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID);\n        if (!stopLocationID || stopLocationID <= 0) {\n            toast({\n                title: \"Error\",\n                description: \"Please select a trip stop location\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        // Validate arriveTime - check both arrTime and tripReport_Stops.arriveTime/arrTime\n        const arriveTimeValue = arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime);\n        // Use isEmpty but also check for false value since arrTime's initial state is false\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(arriveTimeValue) || arriveTimeValue === false) {\n            toast({\n                title: \"Error\",\n                description: \"Please enter an arrival time\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        return true;\n    };\n    console.log(\"location\", location);\n    const handleSave = async ()=>{\n        // Validate form before saving\n        if (!validateForm()) {\n            return;\n        }\n        // Use local state values for the most current input data\n        const paxOnValue = localPaxOn !== \"\" ? +localPaxOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn) || 0;\n        const paxOffValue = localPaxOff !== \"\" ? +localPaxOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff) || 0;\n        const vehicleOnValue = localVehicleOn !== \"\" ? +localVehicleOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn) || 0;\n        const vehicleOffValue = localVehicleOff !== \"\" ? +localVehicleOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff) || 0;\n        const variables = {\n            input: {\n                arriveTime: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime),\n                departTime: depTime ? depTime : tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.departTime,\n                paxJoined: paxOnValue,\n                paxDeparted: paxOffValue,\n                vehiclesJoined: isNaN(vehicleOnValue) ? 0 : vehicleOnValue,\n                vehiclesDeparted: isNaN(vehicleOffValue) ? 0 : vehicleOffValue,\n                stopLocationID: +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID),\n                otherCargo: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo,\n                comments: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments,\n                lat: location.latitude.toString(),\n                long: location.longitude.toString(),\n                dangerousGoodsChecklistID: +(dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.id),\n                designatedDangerousGoodsSailing: displayDangerousGoodsSailing\n            }\n        };\n        if (currentEvent) {\n            if (offline) {\n                // updateTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                    ...variables.input\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n            } else {\n                updateTripReport_Stop({\n                    variables: {\n                        input: {\n                            id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            // Set default values for new records\n            variables.input.paxJoined = variables.input.paxJoined || 0;\n            variables.input.paxDeparted = variables.input.paxDeparted || 0;\n            if (offline) {\n                // createTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    ...variables.input,\n                    logBookEntrySectionID: currentTrip.id,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                if (bufferDgr.length > 0) {\n                    Promise.all(bufferDgr.map(async (dgr)=>{\n                        // updateDangerousGoodsRecord\n                        const dgrData = await dangerousGoodsRecordModel.save({\n                            id: dgr.id,\n                            tripReport_StopID: data.id,\n                            type: dgr.type,\n                            comment: dgr.comment\n                        });\n                        // Clear any existing toasts\n                        if ((currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0) {\n                            await getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                        } else {\n                            if (dgrData) {\n                                await getBufferDgr(dgrData.id);\n                            }\n                        }\n                        // createDangerousGoodsChecklist\n                        const dgChecklistData = await dangerousGoodsChecklistModel.save({\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                            tripReport_StopID: data.id,\n                            vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                            bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                            twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                            fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                            noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                            spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                            fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                            dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                            loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                            msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                            anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                            safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                            vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                        });\n                        setDgrChecklist(dgChecklistData);\n                    }));\n                }\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                closeModal();\n            } else {\n                createTripReport_Stop({\n                    variables: {\n                        input: {\n                            ...variables.input,\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createDangerousGoodsChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateDangerousGoodsChecklist, {\n        onCompleted: (response)=>{\n            const data = response.createDangerousGoodsChecklist;\n            setDgrChecklist(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating dangerous goods\", error);\n        }\n    });\n    const [updateDangerousGoodsRecord] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateDangerousGoodsRecord, {\n        onCompleted: (response)=>{\n            const data = response.updateDangerousGoodsRecord;\n            // Process the response\n            (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0 ? getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) : getBufferDgr(data.id);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating dangerous goods record\", error);\n            toast({\n                title: \"Error\",\n                description: \"Error updating dangerous goods record\",\n                variant: \"destructive\"\n            });\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            if (bufferDgr.length > 0) {\n                bufferDgr.map((dgr)=>{\n                    updateDangerousGoodsRecord({\n                        variables: {\n                            input: {\n                                id: dgr.id,\n                                tripReport_StopID: data.id,\n                                type: dgr.type,\n                                comment: dgr.comment\n                            }\n                        }\n                    });\n                    createDangerousGoodsChecklist({\n                        variables: {\n                            input: {\n                                tripReport_StopID: data.id,\n                                vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                                bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                                twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                                fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                                noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                                spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                                fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                                dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                                loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                                msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                                anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                                safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                                vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                            }\n                        }\n                    });\n                });\n            }\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const [updateTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating passenger drop facility\", error);\n        }\n    });\n    const handleCreateNewLocation = async ()=>{\n        const title = document.getElementById(\"new-location-title\");\n        const latitude = document.getElementById(\"new-location-latitude\");\n        const longitude = document.getElementById(\"new-location-longitude\");\n        if (title && latitude && longitude) {\n            if (offline) {\n                // createGeoLocation\n                const data = await geoLocationModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                    title: title.value,\n                    lat: +latitude.value,\n                    long: +longitude.value,\n                    parentID: parentLocation\n                });\n                setLocations([\n                    ...locations,\n                    {\n                        label: data.title,\n                        value: data.id,\n                        latitude: data.lat,\n                        longitude: data.long\n                    }\n                ]);\n                setTripReport_Stops({\n                    ...tripReport_Stops,\n                    geoLocationID: data.id\n                });\n                setOpenNewLocationDialog(false);\n            } else {\n                createGeoLocation({\n                    variables: {\n                        input: {\n                            title: title.value,\n                            lat: +latitude.value,\n                            long: +longitude.value,\n                            parentID: parentLocation\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createGeoLocation] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CREATE_GEO_LOCATION, {\n        onCompleted: (response)=>{\n            const data = response.createGeoLocation;\n            setLocations([\n                ...locations,\n                {\n                    label: data.title,\n                    value: data.id,\n                    latitude: data.lat,\n                    longitude: data.long\n                }\n            ]);\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: data.id\n            });\n            setOpenNewLocationDialog(false);\n        },\n        onError: (error)=>{\n            toast({\n                title: \"Error\",\n                description: \"Error creating GeoLocation: \" + error.message,\n                variant: \"destructive\"\n            });\n            setOpenNewLocationDialog(false);\n            console.error(\"Error creating new location\", error);\n        }\n    });\n    var _tripReport_Stops_depTime;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.P, {\n                className: \"max-w-[40rem] leading-loose\",\n                children: \"For recording trip stops where passengers, cargo and/or vehicles maybe getting on and off.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1064,\n                columnNumber: 13\n            }, this),\n            displayField(type + \"Location\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Location of trip stop\",\n                htmlFor: \"trip-location\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    offline: offline,\n                    setCurrentLocation: setLocation,\n                    handleLocationChange: handleLocationChangeCallback,\n                    currentEvent: currentEventForLocation,\n                    showAddNewLocation: true,\n                    showUseCoordinates: true,\n                    showCurrentLocation: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1073,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1069,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Arrival\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Arrival Time\",\n                htmlFor: \"arrival-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime) || \"\",\n                    buttonLabel: \"Arrive now\",\n                    handleTimeChange: handleArrTimeChange,\n                    timeID: \"arrival-time\",\n                    fieldName: \"Arrival Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1089,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1085,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Departure\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Departure Time\",\n                htmlFor: \"departure-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: depTime ? depTime : (_tripReport_Stops_depTime = tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.depTime) !== null && _tripReport_Stops_depTime !== void 0 ? _tripReport_Stops_depTime : \"\",\n                    buttonLabel: \"Depart now\",\n                    handleTimeChange: handleDepTimeChange,\n                    timeID: \"departure-time\",\n                    fieldName: \"Departure Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1108,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1104,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"PaxPickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers off\",\n                        htmlFor: \"paxOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOff\",\n                            name: \"paxOff\",\n                            type: \"number\",\n                            value: localPaxOff,\n                            placeholder: \"Pax off\",\n                            min: \"0\",\n                            onChange: handlePaxOffChange,\n                            onBlur: handlePaxOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1126,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1122,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers on\",\n                        htmlFor: \"paxOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOn\",\n                            name: \"paxOn\",\n                            type: \"number\",\n                            value: localPaxOn,\n                            placeholder: \"Pax on\",\n                            min: \"0\",\n                            onChange: handlePaxOnChange,\n                            onBlur: handlePaxOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1141,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1137,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1120,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"VehiclePickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles on\",\n                        htmlFor: \"vehicleOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOn\",\n                            name: \"vehicleOn\",\n                            type: \"number\",\n                            value: localVehicleOn,\n                            placeholder: \"Vehicles getting on\",\n                            min: \"0\",\n                            onChange: handleVehicleOnChange,\n                            onBlur: handleVehicleOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1161,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1157,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles off\",\n                        htmlFor: \"vehicleOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOff\",\n                            name: \"vehicleOff\",\n                            type: \"number\",\n                            value: localVehicleOff,\n                            placeholder: \"Vehicles getting off\",\n                            min: \"0\",\n                            onChange: handleVehicleOffChange,\n                            onBlur: handleVehicleOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1176,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1172,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1155,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"OtherCargo\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Cargo (if any)\",\n                htmlFor: \"cargo-onOff\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"cargo-onOff\",\n                    placeholder: \"Other cargo on and off\",\n                    value: cargoOnOff !== \"\" ? cargoOnOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo) || \"\",\n                    onChange: (e)=>{\n                        setCargoOnOff(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            otherCargo: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1194,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1190,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pvpddgr__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                offline: offline,\n                locked: locked,\n                currentTrip: currentTrip,\n                logBookConfig: logBookConfig,\n                selectedDGR: selectedDGR,\n                setSelectedDGR: setSelectedDGR,\n                members: members,\n                displayDangerousGoods: displayDangerousGoods,\n                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                allDangerousGoods: allPVPDDangerousGoods,\n                setAllDangerousGoods: setAllPVPDDangerousGoods,\n                currentEvent: tripEvent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1215,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Comments\",\n                htmlFor: \"comments\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"comments\",\n                    placeholder: \"Comments\",\n                    value: comments !== \"\" ? comments : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments) || \"\",\n                    onChange: (e)=>{\n                        setComments(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            comments: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1238,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1234,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                        onClick: ()=>closeModal(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1258,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"primary\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                        onClick: locked ? ()=>{} : handleSave,\n                        disabled: locked,\n                        children: selectedEvent ? \"Update\" : \"Save\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1264,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1257,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.AlertDialogNew, {\n                openDialog: openNewLocationDialog,\n                setOpenDialog: setOpenNewLocationDialog,\n                actionText: \"Add New Location\",\n                handleCreate: handleCreateNewLocation,\n                title: \"Add New Location\",\n                description: \"Create a new location for trip stops\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-title\",\n                            type: \"text\",\n                            \"aria-describedby\": \"title-error\",\n                            required: true,\n                            placeholder: \"Location Title\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1280,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1279,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__.Combobox, {\n                            options: locations || [],\n                            onChange: handleParentLocationChangeCallback,\n                            placeholder: \"Parent Location (Optional)\",\n                            buttonClassName: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1289,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1288,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-latitude\",\n                            type: \"text\",\n                            defaultValue: location.latitude,\n                            \"aria-describedby\": \"latitude-error\",\n                            required: true,\n                            placeholder: \"Latitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1297,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1296,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-longitude\",\n                            type: \"text\",\n                            defaultValue: location.longitude,\n                            \"aria-describedby\": \"longitude-error\",\n                            required: true,\n                            placeholder: \"Longitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1307,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1306,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1272,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n        lineNumber: 1063,\n        columnNumber: 9\n    }, this);\n}\n_s(PassengerVehiclePickDrop, \"1hcJJZnkiYqvOFPKhWVVWAgIbio=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation\n    ];\n});\n_c = PassengerVehiclePickDrop;\nvar _c;\n$RefreshReg$(_c, \"PassengerVehiclePickDrop\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx\n"));

/***/ })

});