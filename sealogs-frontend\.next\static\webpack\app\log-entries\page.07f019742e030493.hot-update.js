"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx":
/*!******************************************************************!*\
  !*** ./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PassengerVehiclePickDrop; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _pvpddgr__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../pvpddgr */ \"(app-pages-browser)/./src/app/ui/logbook/pvpddgr.tsx\");\n/* harmony import */ var _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsRecord */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsRecord.js\");\n/* harmony import */ var _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/tripReport_Stop */ \"(app-pages-browser)/./src/app/offline/models/tripReport_Stop.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsChecklist */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsChecklist.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Create model instances outside component to prevent re-instantiation on every render\nconst dangerousGoodsRecordModel = new _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\nconst tripReport_StopModel = new _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\nconst geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\nconst dangerousGoodsChecklistModel = new _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n\n\n\n\n\n\n\nfunction PassengerVehiclePickDrop(param) {\n    let { geoLocations, currentTrip = false, updateTripReport, selectedEvent = false, tripReport, closeModal, type, logBookConfig, members, locked, tripReport_Stops, setTripReport_Stops, displayDangerousGoods = false, displayDangerousGoodsSailing, setDisplayDangerousGoods, setDisplayDangerousGoodsSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGR, setSelectedDGR, offline = false } = param;\n    _s();\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [arrTime, setArrTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [depTime, setDepTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [cargoOnOff, setCargoOnOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(selectedEvent);\n    const [parentLocation, setParentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [bufferDgr, setBufferDgr] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [dgrChecklist, setDgrChecklist] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [tripEvent, setTripEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [openNewLocationDialog, setOpenNewLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // State for LocationField to track current location selection\n    const [currentEventForLocation, setCurrentEventForLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        geoLocationID: 0,\n        lat: null,\n        long: null\n    });\n    // Local state for input values to prevent focus loss\n    const [localPaxOn, setLocalPaxOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localPaxOff, setLocalPaxOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOn, setLocalVehicleOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOff, setLocalVehicleOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // Memoize displayField results to prevent re-computation on every render\n    const displayFieldResults = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents;\n        const eventTypesConfig = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        const fieldMap = new Map();\n        if ((eventTypesConfig === null || eventTypesConfig === void 0 ? void 0 : eventTypesConfig.length) > 0) {\n            var _eventTypesConfig__customisedComponentFields, _eventTypesConfig_;\n            (_eventTypesConfig_ = eventTypesConfig[0]) === null || _eventTypesConfig_ === void 0 ? void 0 : (_eventTypesConfig__customisedComponentFields = _eventTypesConfig_.customisedComponentFields) === null || _eventTypesConfig__customisedComponentFields === void 0 ? void 0 : _eventTypesConfig__customisedComponentFields.nodes.forEach((field)=>{\n                if (field.status !== \"Off\") {\n                    fieldMap.set(field.fieldName, true);\n                }\n            });\n        }\n        return fieldMap;\n    }, [\n        logBookConfig\n    ]);\n    const displayField = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((fieldName)=>{\n        return displayFieldResults.get(fieldName) || false;\n    }, [\n        displayFieldResults\n    ]);\n    // Stable onChange handlers that only update local state\n    const handlePaxOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOff(e.target.value);\n    }, []);\n    const handlePaxOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOn(e.target.value);\n    }, []);\n    const handleVehicleOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOn(e.target.value);\n    }, []);\n    const handleVehicleOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOff(e.target.value);\n    }, []);\n    // Stable onBlur handlers that update the main state\n    const handlePaxOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handlePaxOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    // Memoize other callback functions\n    const handleLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        console.log(\"\\uD83D\\uDDFA️ LocationChange:\", value);\n        // If value is null or undefined, clear the location\n        if (!value) {\n            console.log(\"\\uD83D\\uDDFA️ Clearing location\");\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: 0,\n                lat: null,\n                long: null\n            });\n            // Update currentEventForLocation for LocationField display\n            setCurrentEventForLocation({\n                geoLocationID: 0,\n                lat: null,\n                long: null\n            });\n            return;\n        }\n        // Handle \"Add new location\" option\n        if (value.value === \"newLocation\") {\n            if (\"geolocation\" in navigator) {\n                navigator.geolocation.getCurrentPosition((param)=>{\n                    let { coords } = param;\n                    const { latitude, longitude } = coords;\n                    setLocation({\n                        latitude,\n                        longitude\n                    });\n                    setOpenNewLocationDialog(true);\n                });\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: \"Geolocation is not supported by your browser\",\n                    variant: \"destructive\"\n                });\n                setOpenNewLocationDialog(true);\n            }\n            return;\n        }\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            console.log(\"\\uD83D\\uDDFA️ Location selected:\", value.label, \"ID:\", value.value);\n            // If the value object has latitude and longitude, update currentLocation\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                console.log(\"\\uD83D\\uDDFA️ Setting coords from location:\", value.latitude, value.longitude);\n                setTripReport_Stops({\n                    ...tripReport_Stops,\n                    geoLocationID: 0,\n                    lat: value.latitude,\n                    long: value.longitude\n                });\n                // Update currentEventForLocation for LocationField display\n                setCurrentEventForLocation({\n                    geoLocationID: 0,\n                    lat: value.latitude,\n                    long: value.longitude\n                });\n                setCurrentLocation({\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                });\n            }\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            console.log(\"\\uD83D\\uDDFA️ Coordinates entered:\", value.latitude, value.longitude);\n            // Handle direct coordinates input\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update currentEventForLocation for LocationField display\n            setCurrentEventForLocation({\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update currentLocation\n            setCurrentLocation({\n                latitude: value.latitude,\n                longitude: value.longitude\n            });\n        }\n    }, [\n        tripReport_Stops,\n        setTripReport_Stops,\n        toast,\n        setLocation,\n        setOpenNewLocationDialog,\n        setCurrentLocation,\n        setCurrentEventForLocation\n    ]);\n    const handleParentLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((selectedLocation)=>{\n        setParentLocation((selectedLocation === null || selectedLocation === void 0 ? void 0 : selectedLocation.value) || null);\n    }, [\n        setParentLocation\n    ]);\n    const handleArrTimeChange = (date)=>{\n        const formattedTime = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\");\n        setArrTime(formattedTime);\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            arriveTime: formattedTime,\n            arrTime: formattedTime\n        });\n    };\n    const handleDepTimeChange = (date)=>{\n        setDepTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            depTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"),\n            departTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\")\n        });\n    };\n    // Initialize local state from tripReport_Stops\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (tripReport_Stops) {\n            var _tripReport_Stops_paxOn, _tripReport_Stops_paxOff, _tripReport_Stops_vehicleOn, _tripReport_Stops_vehicleOff;\n            setLocalPaxOn(((_tripReport_Stops_paxOn = tripReport_Stops.paxOn) === null || _tripReport_Stops_paxOn === void 0 ? void 0 : _tripReport_Stops_paxOn.toString()) || \"\");\n            setLocalPaxOff(((_tripReport_Stops_paxOff = tripReport_Stops.paxOff) === null || _tripReport_Stops_paxOff === void 0 ? void 0 : _tripReport_Stops_paxOff.toString()) || \"\");\n            setLocalVehicleOn(((_tripReport_Stops_vehicleOn = tripReport_Stops.vehicleOn) === null || _tripReport_Stops_vehicleOn === void 0 ? void 0 : _tripReport_Stops_vehicleOn.toString()) || \"\");\n            setLocalVehicleOff(((_tripReport_Stops_vehicleOff = tripReport_Stops.vehicleOff) === null || _tripReport_Stops_vehicleOff === void 0 ? void 0 : _tripReport_Stops_vehicleOff.toString()) || \"\");\n        }\n    }, [\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff\n    ]);\n    // Only initialize from existing records when editing (no automatic sync)\n    const [hasInitialized, setHasInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!hasInitialized && selectedEvent && tripReport_Stops && (tripReport_Stops.geoLocationID || tripReport_Stops.lat || tripReport_Stops.long)) {\n            console.log(\"\\uD83D\\uDDFA️ One-time init from existing record:\", {\n                geoLocationID: tripReport_Stops.geoLocationID,\n                lat: tripReport_Stops.lat,\n                long: tripReport_Stops.long\n            });\n            setCurrentEventForLocation({\n                geoLocationID: tripReport_Stops.geoLocationID || 0,\n                lat: tripReport_Stops.lat || null,\n                long: tripReport_Stops.long || null\n            });\n            setHasInitialized(true);\n        }\n    }, [\n        selectedEvent,\n        hasInitialized\n    ]) // Removed tripReport_Stops dependencies to prevent re-runs\n    ;\n    // Track currentEventForLocation changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDDFA️ currentEventForLocation updated:\", currentEventForLocation);\n    }, [\n        currentEventForLocation\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentTripReport_Stop(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        }\n    }, [\n        selectedEvent\n    ]);\n    const offlineCreateDangerousGoodsChecklist = async ()=>{\n        // createDangerousGoodsChecklist\n        const delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\n        await delay(2000);\n        const data = await dangerousGoodsChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n        });\n        setDgrChecklist(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (currentEvent) {\n            getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            setDgrChecklist(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.dangerousGoodsChecklist);\n        } else {\n            // Initialize default values for new records\n            if (!tripReport_Stops) {\n                setTripReport_Stops({\n                    paxOn: 0,\n                    paxOff: 0,\n                    vehicleOn: 0,\n                    vehicleOff: 0\n                });\n            }\n        }\n    }, [\n        currentEvent\n    ]);\n    const getCurrentTripReport_Stop = async (id)=>{\n        if (offline) {\n            // tripReport_Stop\n            const event = await tripReport_StopModel.getById(id);\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3, _event_stopLocation4, _event_stopLocation5;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long\n                    });\n                    // Initialize currentEventForLocation for LocationField\n                    const locationData = {\n                        geoLocationID: event.stopLocationID || 0,\n                        lat: ((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) || (event === null || event === void 0 ? void 0 : event.lat) || null,\n                        long: ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long) || (event === null || event === void 0 ? void 0 : event.long) || null\n                    };\n                    console.log(\"\\uD83D\\uDDFA️ Loading offline data:\", locationData);\n                    setCurrentEventForLocation(locationData);\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat) && ((_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long)) {\n                        var _event_stopLocation6, _event_stopLocation7;\n                        console.log(\"\\uD83D\\uDDFA️ Stop location:\", event);\n                        setCurrentLocation({\n                            latitude: (_event_stopLocation6 = event.stopLocation) === null || _event_stopLocation6 === void 0 ? void 0 : _event_stopLocation6.lat,\n                            longitude: (_event_stopLocation7 = event.stopLocation) === null || _event_stopLocation7 === void 0 ? void 0 : _event_stopLocation7.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        console.log(\"\\uD83D\\uDDFA️ Stop lat long:\", event);\n                        setCurrentLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        } else {\n            tripReport_Stop({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const getBufferDgr = async (id)=>{\n        if (bufferDgr.length > 0) {\n            const dgr = bufferDgr.map((d)=>{\n                return +d.id;\n            });\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    ...dgr,\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            ...dgr,\n                            +id\n                        ]\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            +id\n                        ]\n                    }\n                });\n            }\n        }\n    };\n    const [getDgrList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetDangerousGoodsRecords, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setBufferDgr(data.readDangerousGoodsRecords.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error getting buffer dgr\", error);\n        }\n    });\n    const [tripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetTripReport_Stop, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripReport_Stop;\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setDisplayDangerousGoodsSailing(displayDangerousGoodsSailing !== null ? displayDangerousGoodsSailing : event === null || event === void 0 ? void 0 : event.designatedDangerousGoodsSailing);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3, _event_stopLocation4, _event_stopLocation5;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long,\n                        designatedDangerousGoodsSailing: event.designatedDangerousGoodsSailing\n                    });\n                    // Initialize currentEventForLocation for LocationField\n                    const locationData = {\n                        geoLocationID: event.stopLocationID || 0,\n                        lat: ((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) || (event === null || event === void 0 ? void 0 : event.lat) || null,\n                        long: ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long) || (event === null || event === void 0 ? void 0 : event.long) || null\n                    };\n                    console.log(\"\\uD83D\\uDDFA️ Loading online data:\", locationData);\n                    setCurrentEventForLocation(locationData);\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat) && ((_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long)) {\n                        var _event_stopLocation6, _event_stopLocation7;\n                        console.log(\"\\uD83D\\uDDFA️ stopLocation trip:\", event);\n                        setCurrentLocation({\n                            latitude: (_event_stopLocation6 = event.stopLocation) === null || _event_stopLocation6 === void 0 ? void 0 : _event_stopLocation6.lat,\n                            longitude: (_event_stopLocation7 = event.stopLocation) === null || _event_stopLocation7 === void 0 ? void 0 : _event_stopLocation7.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        console.log(\"\\uD83D\\uDDFA️ Stop lat long trip:\", event);\n                        setCurrentLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    // Memoize locations array to prevent unnecessary re-creation\n    const memoizedLocations = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (!geoLocations) return [];\n        return [\n            {\n                label: \"--- Add new location ---\",\n                value: \"newLocation\"\n            },\n            ...geoLocations.filter((location)=>location.title).map((location)=>({\n                    label: location.title,\n                    value: location.id,\n                    latitude: location.lat,\n                    longitude: location.long\n                }))\n        ];\n    }, [\n        geoLocations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setLocations(memoizedLocations);\n    }, [\n        memoizedLocations\n    ]);\n    const validateForm = ()=>{\n        // Validate stopLocationID\n        const stopLocationID = +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID);\n        if (!stopLocationID || stopLocationID <= 0) {\n            toast({\n                title: \"Error\",\n                description: \"Please select a trip stop location\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        // Validate arriveTime - check both arrTime and tripReport_Stops.arriveTime/arrTime\n        const arriveTimeValue = arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime);\n        // Use isEmpty but also check for false value since arrTime's initial state is false\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(arriveTimeValue) || arriveTimeValue === false) {\n            toast({\n                title: \"Error\",\n                description: \"Please enter an arrival time\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        return true;\n    };\n    console.log(\"currentLocation\", currentLocation);\n    const handleSave = async ()=>{\n        // Validate form before saving\n        if (!validateForm()) {\n            return;\n        }\n        // Use local state values for the most current input data\n        const paxOnValue = localPaxOn !== \"\" ? +localPaxOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn) || 0;\n        const paxOffValue = localPaxOff !== \"\" ? +localPaxOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff) || 0;\n        const vehicleOnValue = localVehicleOn !== \"\" ? +localVehicleOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn) || 0;\n        const vehicleOffValue = localVehicleOff !== \"\" ? +localVehicleOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff) || 0;\n        const variables = {\n            input: {\n                arriveTime: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime),\n                departTime: depTime ? depTime : tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.departTime,\n                paxJoined: paxOnValue,\n                paxDeparted: paxOffValue,\n                vehiclesJoined: isNaN(vehicleOnValue) ? 0 : vehicleOnValue,\n                vehiclesDeparted: isNaN(vehicleOffValue) ? 0 : vehicleOffValue,\n                stopLocationID: +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID),\n                otherCargo: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo,\n                comments: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments,\n                lat: currentLocation.latitude.toString(),\n                long: currentLocation.longitude.toString(),\n                dangerousGoodsChecklistID: +(dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.id),\n                designatedDangerousGoodsSailing: displayDangerousGoodsSailing\n            }\n        };\n        if (currentEvent) {\n            if (offline) {\n                // updateTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                    ...variables.input\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n            } else {\n                updateTripReport_Stop({\n                    variables: {\n                        input: {\n                            id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            // Set default values for new records\n            variables.input.paxJoined = variables.input.paxJoined || 0;\n            variables.input.paxDeparted = variables.input.paxDeparted || 0;\n            if (offline) {\n                // createTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    ...variables.input,\n                    logBookEntrySectionID: currentTrip.id,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                if (bufferDgr.length > 0) {\n                    Promise.all(bufferDgr.map(async (dgr)=>{\n                        // updateDangerousGoodsRecord\n                        const dgrData = await dangerousGoodsRecordModel.save({\n                            id: dgr.id,\n                            tripReport_StopID: data.id,\n                            type: dgr.type,\n                            comment: dgr.comment\n                        });\n                        // Clear any existing toasts\n                        if ((currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0) {\n                            await getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                        } else {\n                            if (dgrData) {\n                                await getBufferDgr(dgrData.id);\n                            }\n                        }\n                        // createDangerousGoodsChecklist\n                        const dgChecklistData = await dangerousGoodsChecklistModel.save({\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                            tripReport_StopID: data.id,\n                            vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                            bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                            twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                            fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                            noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                            spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                            fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                            dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                            loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                            msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                            anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                            safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                            vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                        });\n                        setDgrChecklist(dgChecklistData);\n                    }));\n                }\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                closeModal();\n            } else {\n                createTripReport_Stop({\n                    variables: {\n                        input: {\n                            ...variables.input,\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createDangerousGoodsChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateDangerousGoodsChecklist, {\n        onCompleted: (response)=>{\n            const data = response.createDangerousGoodsChecklist;\n            setDgrChecklist(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating dangerous goods\", error);\n        }\n    });\n    const [updateDangerousGoodsRecord] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateDangerousGoodsRecord, {\n        onCompleted: (response)=>{\n            const data = response.updateDangerousGoodsRecord;\n            // Process the response\n            (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0 ? getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) : getBufferDgr(data.id);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating dangerous goods record\", error);\n            toast({\n                title: \"Error\",\n                description: \"Error updating dangerous goods record\",\n                variant: \"destructive\"\n            });\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            if (bufferDgr.length > 0) {\n                bufferDgr.map((dgr)=>{\n                    updateDangerousGoodsRecord({\n                        variables: {\n                            input: {\n                                id: dgr.id,\n                                tripReport_StopID: data.id,\n                                type: dgr.type,\n                                comment: dgr.comment\n                            }\n                        }\n                    });\n                    createDangerousGoodsChecklist({\n                        variables: {\n                            input: {\n                                tripReport_StopID: data.id,\n                                vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                                bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                                twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                                fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                                noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                                spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                                fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                                dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                                loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                                msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                                anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                                safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                                vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                            }\n                        }\n                    });\n                });\n            }\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const [updateTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating passenger drop facility\", error);\n        }\n    });\n    const handleCreateNewLocation = async ()=>{\n        const title = document.getElementById(\"new-location-title\");\n        const latitude = document.getElementById(\"new-location-latitude\");\n        const longitude = document.getElementById(\"new-location-longitude\");\n        if (title && latitude && longitude) {\n            if (offline) {\n                // createGeoLocation\n                const data = await geoLocationModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                    title: title.value,\n                    lat: +latitude.value,\n                    long: +longitude.value,\n                    parentID: parentLocation\n                });\n                setLocations([\n                    ...locations,\n                    {\n                        label: data.title,\n                        value: data.id,\n                        latitude: data.lat,\n                        longitude: data.long\n                    }\n                ]);\n                setTripReport_Stops({\n                    ...tripReport_Stops,\n                    geoLocationID: data.id\n                });\n                setOpenNewLocationDialog(false);\n            } else {\n                createGeoLocation({\n                    variables: {\n                        input: {\n                            title: title.value,\n                            lat: +latitude.value,\n                            long: +longitude.value,\n                            parentID: parentLocation\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createGeoLocation] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CREATE_GEO_LOCATION, {\n        onCompleted: (response)=>{\n            const data = response.createGeoLocation;\n            setLocations([\n                ...locations,\n                {\n                    label: data.title,\n                    value: data.id,\n                    latitude: data.lat,\n                    longitude: data.long\n                }\n            ]);\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: data.id\n            });\n            setOpenNewLocationDialog(false);\n        },\n        onError: (error)=>{\n            toast({\n                title: \"Error\",\n                description: \"Error creating GeoLocation: \" + error.message,\n                variant: \"destructive\"\n            });\n            setOpenNewLocationDialog(false);\n            console.error(\"Error creating new location\", error);\n        }\n    });\n    var _tripReport_Stops_depTime;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.P, {\n                className: \"max-w-[40rem] leading-loose\",\n                children: \"For recording trip stops where passengers, cargo and/or vehicles maybe getting on and off.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1021,\n                columnNumber: 13\n            }, this),\n            displayField(type + \"Location\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Location of trip stop\",\n                htmlFor: \"trip-location\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    offline: offline,\n                    setCurrentLocation: setCurrentLocation,\n                    handleLocationChange: handleLocationChangeCallback,\n                    currentEvent: currentEventForLocation\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1030,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1026,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Arrival\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Arrival Time\",\n                htmlFor: \"arrival-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime) || \"\",\n                    buttonLabel: \"Arrive now\",\n                    handleTimeChange: handleArrTimeChange,\n                    timeID: \"arrival-time\",\n                    fieldName: \"Arrival Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1043,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1039,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Departure\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Departure Time\",\n                htmlFor: \"departure-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: depTime ? depTime : (_tripReport_Stops_depTime = tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.depTime) !== null && _tripReport_Stops_depTime !== void 0 ? _tripReport_Stops_depTime : \"\",\n                    buttonLabel: \"Depart now\",\n                    handleTimeChange: handleDepTimeChange,\n                    timeID: \"departure-time\",\n                    fieldName: \"Departure Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1062,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1058,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"PaxPickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers off\",\n                        htmlFor: \"paxOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOff\",\n                            name: \"paxOff\",\n                            type: \"number\",\n                            value: localPaxOff,\n                            placeholder: \"Pax off\",\n                            min: \"0\",\n                            onChange: handlePaxOffChange,\n                            onBlur: handlePaxOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1080,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1076,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers on\",\n                        htmlFor: \"paxOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOn\",\n                            name: \"paxOn\",\n                            type: \"number\",\n                            value: localPaxOn,\n                            placeholder: \"Pax on\",\n                            min: \"0\",\n                            onChange: handlePaxOnChange,\n                            onBlur: handlePaxOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1095,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1091,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1074,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"VehiclePickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles on\",\n                        htmlFor: \"vehicleOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOn\",\n                            name: \"vehicleOn\",\n                            type: \"number\",\n                            value: localVehicleOn,\n                            placeholder: \"Vehicles getting on\",\n                            min: \"0\",\n                            onChange: handleVehicleOnChange,\n                            onBlur: handleVehicleOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1115,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1111,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles off\",\n                        htmlFor: \"vehicleOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOff\",\n                            name: \"vehicleOff\",\n                            type: \"number\",\n                            value: localVehicleOff,\n                            placeholder: \"Vehicles getting off\",\n                            min: \"0\",\n                            onChange: handleVehicleOffChange,\n                            onBlur: handleVehicleOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1130,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1126,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1109,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"OtherCargo\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Cargo (if any)\",\n                htmlFor: \"cargo-onOff\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"cargo-onOff\",\n                    placeholder: \"Other cargo on and off\",\n                    value: cargoOnOff !== \"\" ? cargoOnOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo) || \"\",\n                    onChange: (e)=>{\n                        setCargoOnOff(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            otherCargo: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1148,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1144,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pvpddgr__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                offline: offline,\n                locked: locked,\n                currentTrip: currentTrip,\n                logBookConfig: logBookConfig,\n                selectedDGR: selectedDGR,\n                setSelectedDGR: setSelectedDGR,\n                members: members,\n                displayDangerousGoods: displayDangerousGoods,\n                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                allDangerousGoods: allPVPDDangerousGoods,\n                setAllDangerousGoods: setAllPVPDDangerousGoods,\n                currentEvent: tripEvent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1169,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Comments\",\n                htmlFor: \"comments\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"comments\",\n                    placeholder: \"Comments\",\n                    value: comments !== \"\" ? comments : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments) || \"\",\n                    onChange: (e)=>{\n                        setComments(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            comments: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1192,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1188,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                        onClick: ()=>closeModal(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1212,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"primary\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                        onClick: locked ? ()=>{} : handleSave,\n                        disabled: locked,\n                        children: selectedEvent ? \"Update\" : \"Save\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1218,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1211,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.AlertDialogNew, {\n                openDialog: openNewLocationDialog,\n                setOpenDialog: setOpenNewLocationDialog,\n                actionText: \"Add New Location\",\n                handleCreate: handleCreateNewLocation,\n                title: \"Add New Location\",\n                description: \"Create a new location for trip stops\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-title\",\n                            type: \"text\",\n                            \"aria-describedby\": \"title-error\",\n                            required: true,\n                            placeholder: \"Location Title\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1234,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1233,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__.Combobox, {\n                            options: locations || [],\n                            onChange: handleParentLocationChangeCallback,\n                            placeholder: \"Parent Location (Optional)\",\n                            buttonClassName: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1243,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1242,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-latitude\",\n                            type: \"text\",\n                            defaultValue: location.latitude,\n                            \"aria-describedby\": \"latitude-error\",\n                            required: true,\n                            placeholder: \"Latitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1251,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1250,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-longitude\",\n                            type: \"text\",\n                            defaultValue: location.longitude,\n                            \"aria-describedby\": \"longitude-error\",\n                            required: true,\n                            placeholder: \"Longitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1261,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1260,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1226,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n        lineNumber: 1020,\n        columnNumber: 9\n    }, this);\n}\n_s(PassengerVehiclePickDrop, \"1SZ884mojrQwYbjj1rzEYaLln5k=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation\n    ];\n});\n_c = PassengerVehiclePickDrop;\nvar _c;\n$RefreshReg$(_c, \"PassengerVehiclePickDrop\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx\n"));

/***/ })

});