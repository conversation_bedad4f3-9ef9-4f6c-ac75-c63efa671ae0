"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx":
/*!******************************************************************!*\
  !*** ./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PassengerVehiclePickDrop; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _pvpddgr__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../pvpddgr */ \"(app-pages-browser)/./src/app/ui/logbook/pvpddgr.tsx\");\n/* harmony import */ var _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsRecord */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsRecord.js\");\n/* harmony import */ var _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/tripReport_Stop */ \"(app-pages-browser)/./src/app/offline/models/tripReport_Stop.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsChecklist */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsChecklist.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Create model instances outside component to prevent re-instantiation on every render\nconst dangerousGoodsRecordModel = new _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\nconst tripReport_StopModel = new _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\nconst geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\nconst dangerousGoodsChecklistModel = new _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n\n\n\n\n\n\n\nfunction PassengerVehiclePickDrop(param) {\n    let { geoLocations, currentTrip = false, updateTripReport, selectedEvent = false, tripReport, closeModal, type, logBookConfig, members, locked, tripReport_Stops, setTripReport_Stops, displayDangerousGoods = false, displayDangerousGoodsSailing, setDisplayDangerousGoods, setDisplayDangerousGoodsSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGR, setSelectedDGR, offline = false } = param;\n    _s();\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [arrTime, setArrTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [depTime, setDepTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [cargoOnOff, setCargoOnOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(selectedEvent);\n    const [parentLocation, setParentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [bufferDgr, setBufferDgr] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [dgrChecklist, setDgrChecklist] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [tripEvent, setTripEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: 0,\n        longitude: 0\n    });\n    const [openNewLocationDialog, setOpenNewLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // State for LocationField to track current location selection\n    const [currentEventForLocation, setCurrentEventForLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        geoLocationID: 0,\n        lat: null,\n        long: null\n    });\n    // Local state for input values to prevent focus loss\n    const [localPaxOn, setLocalPaxOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localPaxOff, setLocalPaxOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOn, setLocalVehicleOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOff, setLocalVehicleOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // Memoize displayField results to prevent re-computation on every render\n    const displayFieldResults = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents;\n        const eventTypesConfig = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        const fieldMap = new Map();\n        if ((eventTypesConfig === null || eventTypesConfig === void 0 ? void 0 : eventTypesConfig.length) > 0) {\n            var _eventTypesConfig__customisedComponentFields, _eventTypesConfig_;\n            (_eventTypesConfig_ = eventTypesConfig[0]) === null || _eventTypesConfig_ === void 0 ? void 0 : (_eventTypesConfig__customisedComponentFields = _eventTypesConfig_.customisedComponentFields) === null || _eventTypesConfig__customisedComponentFields === void 0 ? void 0 : _eventTypesConfig__customisedComponentFields.nodes.forEach((field)=>{\n                if (field.status !== \"Off\") {\n                    fieldMap.set(field.fieldName, true);\n                }\n            });\n        }\n        return fieldMap;\n    }, [\n        logBookConfig\n    ]);\n    const displayField = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((fieldName)=>{\n        return displayFieldResults.get(fieldName) || false;\n    }, [\n        displayFieldResults\n    ]);\n    // Stable onChange handlers that only update local state\n    const handlePaxOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOff(e.target.value);\n    }, []);\n    const handlePaxOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOn(e.target.value);\n    }, []);\n    const handleVehicleOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOn(e.target.value);\n    }, []);\n    const handleVehicleOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOff(e.target.value);\n    }, []);\n    // Stable onBlur handlers that update the main state\n    const handlePaxOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handlePaxOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    // Helper functions for location handling\n    const clearLocationData = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDDFA️ Clearing location\");\n        const clearedData = {\n            geoLocationID: 0,\n            lat: null,\n            long: null\n        };\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                ...clearedData\n            }));\n        setCurrentEventForLocation(clearedData);\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleNewLocationRequest = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (\"geolocation\" in navigator) {\n            navigator.geolocation.getCurrentPosition((param)=>{\n                let { coords } = param;\n                const { latitude, longitude } = coords;\n                setLocation({\n                    latitude,\n                    longitude\n                });\n                setOpenNewLocationDialog(true);\n            });\n        } else {\n            toast({\n                title: \"Error\",\n                description: \"Geolocation is not supported by your browser\",\n                variant: \"destructive\"\n            });\n            setOpenNewLocationDialog(true);\n        }\n    }, [\n        setLocation,\n        setOpenNewLocationDialog,\n        toast\n    ]);\n    const handleLocationSelection = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        console.log(\"\\uD83D\\uDDFA️ Location selected:\", value.label, \"ID:\", value.value);\n        const locationData = {\n            geoLocationID: value.value,\n            lat: value.latitude || null,\n            long: value.longitude || null\n        };\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                ...locationData\n            }));\n        setCurrentEventForLocation(locationData);\n        setLocation({\n            latitude: value.latitude || 0,\n            longitude: value.longitude || 0\n        });\n    }, [\n        setTripReport_Stops,\n        setLocation\n    ]);\n    const handleCoordinatesInput = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        console.log(\"\\uD83D\\uDDFA️ Coordinates entered:\", value.latitude, value.longitude);\n        const coordinatesData = {\n            geoLocationID: 0,\n            lat: value.latitude,\n            long: value.longitude\n        };\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                ...coordinatesData\n            }));\n        setCurrentEventForLocation(coordinatesData);\n        setLocation({\n            latitude: value.latitude,\n            longitude: value.longitude\n        });\n    }, [\n        setTripReport_Stops,\n        setLocation\n    ]);\n    // Memoize main callback function\n    const handleLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        console.log(\"\\uD83D\\uDDFA️ LocationChange:\", value);\n        // If value is null or undefined, clear the location\n        if (!value) {\n            clearLocationData();\n            return;\n        }\n        // Handle \"Add new location\" option\n        if (value.value === \"newLocation\") {\n            handleNewLocationRequest();\n            return;\n        }\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // If the value object has latitude and longitude, handle location selection\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                handleLocationSelection(value);\n            }\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            handleCoordinatesInput(value);\n        }\n    }, [\n        clearLocationData,\n        handleNewLocationRequest,\n        handleLocationSelection,\n        handleCoordinatesInput\n    ]);\n    const handleParentLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((selectedLocation)=>{\n        setParentLocation((selectedLocation === null || selectedLocation === void 0 ? void 0 : selectedLocation.value) || null);\n    }, [\n        setParentLocation\n    ]);\n    const handleArrTimeChange = (date)=>{\n        const formattedTime = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\");\n        setArrTime(formattedTime);\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            arriveTime: formattedTime,\n            arrTime: formattedTime\n        });\n    };\n    const handleDepTimeChange = (date)=>{\n        setDepTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            depTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"),\n            departTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\")\n        });\n    };\n    // Initialize local state from tripReport_Stops\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (tripReport_Stops) {\n            var _tripReport_Stops_paxOn, _tripReport_Stops_paxOff, _tripReport_Stops_vehicleOn, _tripReport_Stops_vehicleOff;\n            setLocalPaxOn(((_tripReport_Stops_paxOn = tripReport_Stops.paxOn) === null || _tripReport_Stops_paxOn === void 0 ? void 0 : _tripReport_Stops_paxOn.toString()) || \"\");\n            setLocalPaxOff(((_tripReport_Stops_paxOff = tripReport_Stops.paxOff) === null || _tripReport_Stops_paxOff === void 0 ? void 0 : _tripReport_Stops_paxOff.toString()) || \"\");\n            setLocalVehicleOn(((_tripReport_Stops_vehicleOn = tripReport_Stops.vehicleOn) === null || _tripReport_Stops_vehicleOn === void 0 ? void 0 : _tripReport_Stops_vehicleOn.toString()) || \"\");\n            setLocalVehicleOff(((_tripReport_Stops_vehicleOff = tripReport_Stops.vehicleOff) === null || _tripReport_Stops_vehicleOff === void 0 ? void 0 : _tripReport_Stops_vehicleOff.toString()) || \"\");\n        }\n    }, [\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff\n    ]);\n    // Only initialize from existing records when editing (no automatic sync)\n    const [hasInitialized, setHasInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!hasInitialized && selectedEvent && tripReport_Stops && (tripReport_Stops.geoLocationID || tripReport_Stops.lat || tripReport_Stops.long)) {\n            console.log(\"\\uD83D\\uDDFA️ One-time init from existing record:\", {\n                geoLocationID: tripReport_Stops.geoLocationID,\n                lat: tripReport_Stops.lat,\n                long: tripReport_Stops.long\n            });\n            setCurrentEventForLocation({\n                geoLocationID: tripReport_Stops.geoLocationID || 0,\n                lat: tripReport_Stops.lat || null,\n                long: tripReport_Stops.long || null\n            });\n            setHasInitialized(true);\n        }\n    }, [\n        selectedEvent,\n        hasInitialized\n    ]) // Removed tripReport_Stops dependencies to prevent re-runs\n    ;\n    // Track currentEventForLocation changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDDFA️ currentEventForLocation updated:\", currentEventForLocation);\n    }, [\n        currentEventForLocation\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentTripReport_Stop(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        }\n    }, [\n        selectedEvent\n    ]);\n    const offlineCreateDangerousGoodsChecklist = async ()=>{\n        // createDangerousGoodsChecklist\n        const delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\n        await delay(2000);\n        const data = await dangerousGoodsChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n        });\n        setDgrChecklist(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (currentEvent) {\n            getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            setDgrChecklist(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.dangerousGoodsChecklist);\n        } else {\n            // Initialize default values for new records\n            if (!tripReport_Stops) {\n                setTripReport_Stops({\n                    paxOn: 0,\n                    paxOff: 0,\n                    vehicleOn: 0,\n                    vehicleOff: 0\n                });\n            }\n        }\n    }, [\n        currentEvent\n    ]);\n    const getCurrentTripReport_Stop = async (id)=>{\n        if (offline) {\n            // tripReport_Stop\n            const event = await tripReport_StopModel.getById(id);\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3, _event_stopLocation4, _event_stopLocation5;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long\n                    });\n                    // Initialize currentEventForLocation for LocationField\n                    const locationData = {\n                        geoLocationID: event.stopLocationID || 0,\n                        lat: ((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) || (event === null || event === void 0 ? void 0 : event.lat) || null,\n                        long: ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long) || (event === null || event === void 0 ? void 0 : event.long) || null\n                    };\n                    console.log(\"\\uD83D\\uDDFA️ Loading offline data:\", locationData);\n                    setCurrentEventForLocation(locationData);\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat) && ((_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long)) {\n                        var _event_stopLocation6, _event_stopLocation7;\n                        console.log(\"\\uD83D\\uDDFA️ Stop location:\", event);\n                        setLocation({\n                            latitude: (_event_stopLocation6 = event.stopLocation) === null || _event_stopLocation6 === void 0 ? void 0 : _event_stopLocation6.lat,\n                            longitude: (_event_stopLocation7 = event.stopLocation) === null || _event_stopLocation7 === void 0 ? void 0 : _event_stopLocation7.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        console.log(\"\\uD83D\\uDDFA️ Stop lat long:\", event);\n                        setLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        } else {\n            tripReport_Stop({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const getBufferDgr = async (id)=>{\n        if (bufferDgr.length > 0) {\n            const dgr = bufferDgr.map((d)=>{\n                return +d.id;\n            });\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    ...dgr,\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            ...dgr,\n                            +id\n                        ]\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            +id\n                        ]\n                    }\n                });\n            }\n        }\n    };\n    const [getDgrList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetDangerousGoodsRecords, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setBufferDgr(data.readDangerousGoodsRecords.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error getting buffer dgr\", error);\n        }\n    });\n    const [tripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetTripReport_Stop, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripReport_Stop;\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setDisplayDangerousGoodsSailing(displayDangerousGoodsSailing !== null ? displayDangerousGoodsSailing : event === null || event === void 0 ? void 0 : event.designatedDangerousGoodsSailing);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3, _event_stopLocation4, _event_stopLocation5;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long,\n                        designatedDangerousGoodsSailing: event.designatedDangerousGoodsSailing\n                    });\n                    // Initialize currentEventForLocation for LocationField\n                    const locationData = {\n                        geoLocationID: event.stopLocationID || 0,\n                        lat: ((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) || (event === null || event === void 0 ? void 0 : event.lat) || null,\n                        long: ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long) || (event === null || event === void 0 ? void 0 : event.long) || null\n                    };\n                    console.log(\"\\uD83D\\uDDFA️ Loading online data:\", locationData);\n                    setCurrentEventForLocation(locationData);\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat) && ((_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long)) {\n                        var _event_stopLocation6, _event_stopLocation7;\n                        console.log(\"\\uD83D\\uDDFA️ stopLocation trip:\", event);\n                        setLocation({\n                            latitude: (_event_stopLocation6 = event.stopLocation) === null || _event_stopLocation6 === void 0 ? void 0 : _event_stopLocation6.lat,\n                            longitude: (_event_stopLocation7 = event.stopLocation) === null || _event_stopLocation7 === void 0 ? void 0 : _event_stopLocation7.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        console.log(\"\\uD83D\\uDDFA️ Stop lat long trip:\", event);\n                        setLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    // Memoize locations array to prevent unnecessary re-creation\n    const memoizedLocations = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (!geoLocations) return [];\n        return [\n            {\n                label: \"--- Add new location ---\",\n                value: \"newLocation\"\n            },\n            ...geoLocations.filter((location)=>location.title).map((location)=>({\n                    label: location.title,\n                    value: location.id,\n                    latitude: location.lat,\n                    longitude: location.long\n                }))\n        ];\n    }, [\n        geoLocations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setLocations(memoizedLocations);\n    }, [\n        memoizedLocations\n    ]);\n    // Synchronize currentEventForLocation and location with tripReport_Stops\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (tripReport_Stops) {\n            setCurrentEventForLocation({\n                geoLocationID: tripReport_Stops.geoLocationID || 0,\n                lat: tripReport_Stops.lat,\n                long: tripReport_Stops.long\n            });\n            // Also update location state if coordinates exist\n            if (tripReport_Stops.lat && tripReport_Stops.long) {\n                setLocation({\n                    latitude: tripReport_Stops.lat,\n                    longitude: tripReport_Stops.long\n                });\n            }\n        }\n    }, [\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.lat,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.long\n    ]);\n    const validateForm = ()=>{\n        // Validate location - either geoLocationID > 0 OR valid coordinates\n        const stopLocationID = +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID);\n        const hasValidCoordinates = (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.lat) && (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.long);\n        console.log(\"\\uD83D\\uDD0D Validation check:\", {\n            stopLocationID,\n            hasValidCoordinates,\n            tripReport_Stops: tripReport_Stops\n        });\n        if ((!stopLocationID || stopLocationID <= 0) && !hasValidCoordinates) {\n            toast({\n                title: \"Error\",\n                description: \"Please select a trip stop location or enter coordinates\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        // Validate arriveTime - check both arrTime and tripReport_Stops.arriveTime/arrTime\n        const arriveTimeValue = arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime);\n        // Use isEmpty but also check for false value since arrTime's initial state is false\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(arriveTimeValue) || arriveTimeValue === false) {\n            toast({\n                title: \"Error\",\n                description: \"Please enter an arrival time\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        return true;\n    };\n    console.log(\"location\", location);\n    const handleSave = async ()=>{\n        // Validate form before saving\n        if (!validateForm()) {\n            return;\n        }\n        // Use local state values for the most current input data\n        const paxOnValue = localPaxOn !== \"\" ? +localPaxOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn) || 0;\n        const paxOffValue = localPaxOff !== \"\" ? +localPaxOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff) || 0;\n        const vehicleOnValue = localVehicleOn !== \"\" ? +localVehicleOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn) || 0;\n        const vehicleOffValue = localVehicleOff !== \"\" ? +localVehicleOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff) || 0;\n        const variables = {\n            input: {\n                arriveTime: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime),\n                departTime: depTime ? depTime : tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.departTime,\n                paxJoined: paxOnValue,\n                paxDeparted: paxOffValue,\n                vehiclesJoined: isNaN(vehicleOnValue) ? 0 : vehicleOnValue,\n                vehiclesDeparted: isNaN(vehicleOffValue) ? 0 : vehicleOffValue,\n                stopLocationID: +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID),\n                otherCargo: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo,\n                comments: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments,\n                lat: location.latitude.toString(),\n                long: location.longitude.toString(),\n                dangerousGoodsChecklistID: +(dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.id),\n                designatedDangerousGoodsSailing: displayDangerousGoodsSailing\n            }\n        };\n        if (currentEvent) {\n            if (offline) {\n                // updateTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                    ...variables.input\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n            } else {\n                updateTripReport_Stop({\n                    variables: {\n                        input: {\n                            id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            // Set default values for new records\n            variables.input.paxJoined = variables.input.paxJoined || 0;\n            variables.input.paxDeparted = variables.input.paxDeparted || 0;\n            if (offline) {\n                // createTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    ...variables.input,\n                    logBookEntrySectionID: currentTrip.id,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                if (bufferDgr.length > 0) {\n                    Promise.all(bufferDgr.map(async (dgr)=>{\n                        // updateDangerousGoodsRecord\n                        const dgrData = await dangerousGoodsRecordModel.save({\n                            id: dgr.id,\n                            tripReport_StopID: data.id,\n                            type: dgr.type,\n                            comment: dgr.comment\n                        });\n                        // Clear any existing toasts\n                        if ((currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0) {\n                            await getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                        } else {\n                            if (dgrData) {\n                                await getBufferDgr(dgrData.id);\n                            }\n                        }\n                        // createDangerousGoodsChecklist\n                        const dgChecklistData = await dangerousGoodsChecklistModel.save({\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                            tripReport_StopID: data.id,\n                            vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                            bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                            twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                            fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                            noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                            spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                            fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                            dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                            loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                            msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                            anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                            safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                            vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                        });\n                        setDgrChecklist(dgChecklistData);\n                    }));\n                }\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                closeModal();\n            } else {\n                createTripReport_Stop({\n                    variables: {\n                        input: {\n                            ...variables.input,\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createDangerousGoodsChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateDangerousGoodsChecklist, {\n        onCompleted: (response)=>{\n            const data = response.createDangerousGoodsChecklist;\n            setDgrChecklist(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating dangerous goods\", error);\n        }\n    });\n    const [updateDangerousGoodsRecord] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateDangerousGoodsRecord, {\n        onCompleted: (response)=>{\n            const data = response.updateDangerousGoodsRecord;\n            // Process the response\n            (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0 ? getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) : getBufferDgr(data.id);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating dangerous goods record\", error);\n            toast({\n                title: \"Error\",\n                description: \"Error updating dangerous goods record\",\n                variant: \"destructive\"\n            });\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            if (bufferDgr.length > 0) {\n                bufferDgr.map((dgr)=>{\n                    updateDangerousGoodsRecord({\n                        variables: {\n                            input: {\n                                id: dgr.id,\n                                tripReport_StopID: data.id,\n                                type: dgr.type,\n                                comment: dgr.comment\n                            }\n                        }\n                    });\n                    createDangerousGoodsChecklist({\n                        variables: {\n                            input: {\n                                tripReport_StopID: data.id,\n                                vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                                bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                                twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                                fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                                noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                                spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                                fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                                dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                                loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                                msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                                anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                                safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                                vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                            }\n                        }\n                    });\n                });\n            }\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const [updateTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating passenger drop facility\", error);\n        }\n    });\n    const handleCreateNewLocation = async ()=>{\n        const title = document.getElementById(\"new-location-title\");\n        const latitude = document.getElementById(\"new-location-latitude\");\n        const longitude = document.getElementById(\"new-location-longitude\");\n        if (title && latitude && longitude) {\n            if (offline) {\n                // createGeoLocation\n                const data = await geoLocationModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                    title: title.value,\n                    lat: +latitude.value,\n                    long: +longitude.value,\n                    parentID: parentLocation\n                });\n                setLocations([\n                    ...locations,\n                    {\n                        label: data.title,\n                        value: data.id,\n                        latitude: data.lat,\n                        longitude: data.long\n                    }\n                ]);\n                setTripReport_Stops({\n                    ...tripReport_Stops,\n                    geoLocationID: data.id\n                });\n                setOpenNewLocationDialog(false);\n            } else {\n                createGeoLocation({\n                    variables: {\n                        input: {\n                            title: title.value,\n                            lat: +latitude.value,\n                            long: +longitude.value,\n                            parentID: parentLocation\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createGeoLocation] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CREATE_GEO_LOCATION, {\n        onCompleted: (response)=>{\n            const data = response.createGeoLocation;\n            setLocations([\n                ...locations,\n                {\n                    label: data.title,\n                    value: data.id,\n                    latitude: data.lat,\n                    longitude: data.long\n                }\n            ]);\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: data.id\n            });\n            setOpenNewLocationDialog(false);\n        },\n        onError: (error)=>{\n            toast({\n                title: \"Error\",\n                description: \"Error creating GeoLocation: \" + error.message,\n                variant: \"destructive\"\n            });\n            setOpenNewLocationDialog(false);\n            console.error(\"Error creating new location\", error);\n        }\n    });\n    var _tripReport_Stops_depTime;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.P, {\n                className: \"max-w-[40rem] leading-loose\",\n                children: \"For recording trip stops where passengers, cargo and/or vehicles maybe getting on and off.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1081,\n                columnNumber: 13\n            }, this),\n            displayField(type + \"Location\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Location of trip stop\",\n                htmlFor: \"trip-location\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    offline: offline,\n                    setCurrentLocation: setLocation,\n                    handleLocationChange: handleLocationChangeCallback,\n                    currentEvent: currentEventForLocation,\n                    showAddNewLocation: true,\n                    showUseCoordinates: true,\n                    showCurrentLocation: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1090,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1086,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Arrival\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Arrival Time\",\n                htmlFor: \"arrival-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime) || \"\",\n                    buttonLabel: \"Arrive now\",\n                    handleTimeChange: handleArrTimeChange,\n                    timeID: \"arrival-time\",\n                    fieldName: \"Arrival Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1106,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1102,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Departure\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Departure Time\",\n                htmlFor: \"departure-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: depTime ? depTime : (_tripReport_Stops_depTime = tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.depTime) !== null && _tripReport_Stops_depTime !== void 0 ? _tripReport_Stops_depTime : \"\",\n                    buttonLabel: \"Depart now\",\n                    handleTimeChange: handleDepTimeChange,\n                    timeID: \"departure-time\",\n                    fieldName: \"Departure Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1125,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1121,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"PaxPickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers off\",\n                        htmlFor: \"paxOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOff\",\n                            name: \"paxOff\",\n                            type: \"number\",\n                            value: localPaxOff,\n                            placeholder: \"Pax off\",\n                            min: \"0\",\n                            onChange: handlePaxOffChange,\n                            onBlur: handlePaxOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1143,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1139,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers on\",\n                        htmlFor: \"paxOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOn\",\n                            name: \"paxOn\",\n                            type: \"number\",\n                            value: localPaxOn,\n                            placeholder: \"Pax on\",\n                            min: \"0\",\n                            onChange: handlePaxOnChange,\n                            onBlur: handlePaxOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1158,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1154,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1137,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"VehiclePickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles on\",\n                        htmlFor: \"vehicleOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOn\",\n                            name: \"vehicleOn\",\n                            type: \"number\",\n                            value: localVehicleOn,\n                            placeholder: \"Vehicles getting on\",\n                            min: \"0\",\n                            onChange: handleVehicleOnChange,\n                            onBlur: handleVehicleOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1178,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1174,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles off\",\n                        htmlFor: \"vehicleOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOff\",\n                            name: \"vehicleOff\",\n                            type: \"number\",\n                            value: localVehicleOff,\n                            placeholder: \"Vehicles getting off\",\n                            min: \"0\",\n                            onChange: handleVehicleOffChange,\n                            onBlur: handleVehicleOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1193,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1189,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1172,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"OtherCargo\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Cargo (if any)\",\n                htmlFor: \"cargo-onOff\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"cargo-onOff\",\n                    placeholder: \"Other cargo on and off\",\n                    value: cargoOnOff !== \"\" ? cargoOnOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo) || \"\",\n                    onChange: (e)=>{\n                        setCargoOnOff(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            otherCargo: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1211,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1207,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pvpddgr__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                offline: offline,\n                locked: locked,\n                currentTrip: currentTrip,\n                logBookConfig: logBookConfig,\n                selectedDGR: selectedDGR,\n                setSelectedDGR: setSelectedDGR,\n                members: members,\n                displayDangerousGoods: displayDangerousGoods,\n                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                allDangerousGoods: allPVPDDangerousGoods,\n                setAllDangerousGoods: setAllPVPDDangerousGoods,\n                currentEvent: tripEvent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1232,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Comments\",\n                htmlFor: \"comments\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"comments\",\n                    placeholder: \"Comments\",\n                    value: comments !== \"\" ? comments : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments) || \"\",\n                    onChange: (e)=>{\n                        setComments(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            comments: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1255,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1251,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                        onClick: ()=>closeModal(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1275,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"primary\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                        onClick: locked ? ()=>{} : handleSave,\n                        disabled: locked,\n                        children: selectedEvent ? \"Update\" : \"Save\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1281,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1274,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.AlertDialogNew, {\n                openDialog: openNewLocationDialog,\n                setOpenDialog: setOpenNewLocationDialog,\n                actionText: \"Add New Location\",\n                handleCreate: handleCreateNewLocation,\n                title: \"Add New Location\",\n                description: \"Create a new location for trip stops\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-title\",\n                            type: \"text\",\n                            \"aria-describedby\": \"title-error\",\n                            required: true,\n                            placeholder: \"Location Title\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1297,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1296,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__.Combobox, {\n                            options: locations || [],\n                            onChange: handleParentLocationChangeCallback,\n                            placeholder: \"Parent Location (Optional)\",\n                            buttonClassName: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1306,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1305,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-latitude\",\n                            type: \"text\",\n                            defaultValue: location.latitude,\n                            \"aria-describedby\": \"latitude-error\",\n                            required: true,\n                            placeholder: \"Latitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1314,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1313,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-longitude\",\n                            type: \"text\",\n                            defaultValue: location.longitude,\n                            \"aria-describedby\": \"longitude-error\",\n                            required: true,\n                            placeholder: \"Longitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1324,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1323,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1289,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n        lineNumber: 1080,\n        columnNumber: 9\n    }, this);\n}\n_s(PassengerVehiclePickDrop, \"pcrkLZGmCFkkZvGL0oeFr344mNE=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation\n    ];\n});\n_c = PassengerVehiclePickDrop;\nvar _c;\n$RefreshReg$(_c, \"PassengerVehiclePickDrop\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx\n"));

/***/ })

});